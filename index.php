<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Set page title
$pageTitle = 'Global Logistics & Transport Solutions';

// Get sample tracking numbers for examples
$sampleTrackingNumbers = [];
try {
    $sampleQuery = $conn->query("SELECT tracking_number FROM shipments ORDER BY RAND() LIMIT 3");
    $sampleTrackingNumbers = $sampleQuery->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    // Silently fail
}

// Get popular destinations for examples
$popularDestinations = [
    'New York, USA',
    'Los Angeles, USA',
    'London, UK',
    'Paris, France',
    'Tokyo, Japan',
    'Sydney, Australia'
];

// Include header
include_once 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero gradient-hero textured">
    <div class="container">
        <div class="hero-content">
            <h1>Integrated Logistics Solutions for Global Business</h1>
            <p>We make logistics easy through reliable transport and supply chain services worldwide.</p>
            <div class="cta-buttons">
                <a href="contact.php" class="btn primary-btn glass-btn gradient-btn">Get Started</a>
                <a href="about.php" class="btn secondary-btn glass-btn">Learn More</a>
                <a href="#tracking" class="btn teal-btn glass-btn">Track Shipment</a>
            </div>
        </div>
    </div>
</section>

<!-- Tracking Section -->
<section id="tracking" class="tracking textured gradient-light-section">
    <div class="container">
        <div class="tracking-box">
            <h2>Track Your Shipment</h2>
            <form class="tracking-form" action="tracking/index.php" method="GET">
                <div class="form-group">
                    <label for="tracking-number">Tracking Number</label>
                    <input type="text" id="tracking-number" name="tracking_number" placeholder="Enter your tracking number" required>
                </div>
                <button type="submit" class="btn primary-btn"><i class="fas fa-search"></i> Track</button>
            </form>
            <?php if (!empty($sampleTrackingNumbers)): ?>
            <div class="tracking-examples">
                <p>Example tracking numbers:
                    <?php foreach ($sampleTrackingNumbers as $index => $sample): ?>
                        <a href="tracking/index.php?tracking_number=<?php echo htmlspecialchars($sample); ?>"><?php echo htmlspecialchars($sample); ?></a><?php echo ($index < count($sampleTrackingNumbers) - 1) ? ', ' : ''; ?>
                    <?php endforeach; ?>
                </p>
            </div>
            <?php endif; ?>

            <div class="route-search">
                <h3>Find Routes & Rates</h3>
                <form class="route-form" action="routes.php" method="GET">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="origin">From</label>
                            <input type="text" id="origin" name="origin" placeholder="Origin" required>
                            <div class="location-suggestions" id="origin-suggestions"></div>
                        </div>
                        <div class="form-group">
                            <label for="destination">To</label>
                            <input type="text" id="destination" name="destination" placeholder="Destination" required>
                            <div class="location-suggestions" id="destination-suggestions"></div>
                        </div>
                    </div>
                    <button type="submit" class="btn primary-btn"><i class="fas fa-route"></i> Search</button>
                </form>
                <?php if (!empty($popularDestinations)): ?>
                <div class="popular-destinations">
                    <p>Popular destinations:
                        <?php foreach ($popularDestinations as $index => $destination): ?>
                            <a href="javascript:void(0)" class="destination-link" data-destination="<?php echo htmlspecialchars($destination); ?>"><?php echo htmlspecialchars($destination); ?></a><?php echo ($index < count($popularDestinations) - 1) ? ', ' : ''; ?>
                        <?php endforeach; ?>
                    </p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="services textured-section">
    <div class="container">
        <h2 class="section-title">Our Logistics Solutions</h2>
        <p class="section-subtitle">From warehouse to your doorstep, we develop solutions that meet customer needs across the entire supply chain.</p>

        <div class="services-grid">
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-ship"></i>
                </div>
                <h3>Ocean Freight</h3>
                <p>Reliable container shipping services connecting global markets with efficiency and care.</p>
                <a href="services.php#ocean-freight" class="learn-more">Learn more →</a>
            </div>

            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-truck"></i>
                </div>
                <h3>Land Transport</h3>
                <p>Comprehensive inland logistics solutions for first and last mile delivery needs.</p>
                <a href="services.php#land-transport" class="learn-more">Learn more →</a>
            </div>

            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-warehouse"></i>
                </div>
                <h3>Warehousing</h3>
                <p>Strategic storage solutions with advanced inventory management systems.</p>
                <a href="services.php#warehousing" class="learn-more">Learn more →</a>
            </div>

            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-globe"></i>
                </div>
                <h3>Supply Chain</h3>
                <p>End-to-end supply chain management taking complexity out of logistics for you.</p>
                <a href="services.php#supply-chain" class="learn-more">Learn more →</a>
            </div>
        </div>
    </div>
</section>

<!-- Industries Section -->
<section class="industries gradient-light-section textured-section">
    <div class="container">
        <h2 class="section-title">Industry Sectors We Serve</h2>
        <p class="section-subtitle">Regardless of your industry or key markets, we offer global and local logistics solutions that enable businesses of all sizes to grow.</p>

        <div class="industries-grid">
            <a href="industries.php#retail">
                <div class="industry-card">
                    <div class="industry-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h3>Retail</h3>
                </div>
            </a>

            <a href="industries.php#fashion">
                <div class="industry-card">
                    <div class="industry-icon">
                        <i class="fas fa-tshirt"></i>
                    </div>
                    <h3>Fashion & Lifestyle</h3>
                </div>
            </a>

            <a href="industries.php#chemicals">
                <div class="industry-card">
                    <div class="industry-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <h3>Chemicals</h3>
                </div>
            </a>

            <a href="industries.php#cold-chain">
                <div class="industry-card">
                    <div class="industry-icon">
                        <i class="fas fa-snowflake"></i>
                    </div>
                    <h3>Cold Chain</h3>
                </div>
            </a>

            <a href="industries.php#automotive">
                <div class="industry-card">
                    <div class="industry-icon">
                        <i class="fas fa-car"></i>
                    </div>
                    <h3>Automotive</h3>
                </div>
            </a>

            <div class="industry-card">
                <div class="industry-icon">
                    <i class="fas fa-medkit"></i>
                </div>
                <h3>Healthcare</h3>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="cta-section gradient-primary-section textured">
    <div class="container">
        <div class="cta-content">
            <h2>Ready to Simplify Your Logistics?</h2>
            <p>Register now to access our digital platform and manage your shipments online.</p>
            <a href="#" class="btn primary-btn">Register Now</a>
        </div>
    </div>
</section>

<!-- Location Search JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Location data for suggestions
    const locations = <?php echo json_encode($popularDestinations); ?>;

    // Origin input
    const originInput = document.getElementById('origin');
    const originSuggestions = document.getElementById('origin-suggestions');

    // Destination input
    const destinationInput = document.getElementById('destination');
    const destinationSuggestions = document.getElementById('destination-suggestions');

    // Function to show suggestions
    function showSuggestions(input, suggestionsElement) {
        const value = input.value.toLowerCase();

        // Clear previous suggestions
        suggestionsElement.innerHTML = '';

        if (value.length < 2) {
            suggestionsElement.style.display = 'none';
            return;
        }

        // Filter locations based on input
        const filteredLocations = locations.filter(location =>
            location.toLowerCase().includes(value)
        );

        if (filteredLocations.length === 0) {
            suggestionsElement.style.display = 'none';
            return;
        }

        // Create suggestion elements
        filteredLocations.forEach(location => {
            const suggestion = document.createElement('div');
            suggestion.className = 'suggestion-item';
            suggestion.textContent = location;
            suggestion.addEventListener('click', () => {
                input.value = location;
                suggestionsElement.style.display = 'none';
            });
            suggestionsElement.appendChild(suggestion);
        });

        suggestionsElement.style.display = 'block';
    }

    // Event listeners for origin input
    originInput.addEventListener('input', () => {
        showSuggestions(originInput, originSuggestions);
    });

    originInput.addEventListener('focus', () => {
        if (originInput.value.length >= 2) {
            showSuggestions(originInput, originSuggestions);
        }
    });

    // Event listeners for destination input
    destinationInput.addEventListener('input', () => {
        showSuggestions(destinationInput, destinationSuggestions);
    });

    destinationInput.addEventListener('focus', () => {
        if (destinationInput.value.length >= 2) {
            showSuggestions(destinationInput, destinationSuggestions);
        }
    });

    // Hide suggestions when clicking outside
    document.addEventListener('click', (e) => {
        if (!originInput.contains(e.target) && !originSuggestions.contains(e.target)) {
            originSuggestions.style.display = 'none';
        }

        if (!destinationInput.contains(e.target) && !destinationSuggestions.contains(e.target)) {
            destinationSuggestions.style.display = 'none';
        }
    });

    // Popular destination links
    document.querySelectorAll('.destination-link').forEach(link => {
        link.addEventListener('click', function() {
            const destination = this.getAttribute('data-destination');
            destinationInput.value = destination;

            // If origin is empty, suggest a random origin
            if (!originInput.value) {
                const randomOrigin = locations.filter(loc => loc !== destination)[0];
                originInput.value = randomOrigin;
            }
        });
    });
});
</script>

<!-- Add CSS for location suggestions and mobile fixes -->
<style>
/* Fix for tracking form label on mobile */
@media (max-width: 576px) {
    .tracking-form .form-group {
        position: relative;
        margin-bottom: 30px;
    }

    .tracking-form label {
        display: block;
        margin-bottom: 35px;
        font-size: 0.85rem;
        font-weight: bold;
    }

    .tracking-form input {
        margin-top: 15px;
        padding-top: 20px;
    }

    /* Use absolute positioning for the label to prevent overlap */
    .tracking-form .form-group {
        padding-top: 20px;
    }

    .tracking-form label {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 10;
    }
}

.location-suggestions {
    position: absolute;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0 0 8px 8px;
    z-index: 100;
    display: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.suggestion-item {
    padding: 10px 15px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.suggestion-item:hover {
    background-color: var(--hover-color);
}

.form-group {
    position: relative;
}

.tracking-examples,
.popular-destinations {
    margin-top: 10px;
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-align: center;
}

.tracking-examples a,
.popular-destinations a {
    color: var(--primary-color);
    text-decoration: underline;
    transition: color 0.3s;
}

.tracking-examples a:hover,
.popular-destinations a:hover {
    color: var(--secondary-color);
}
</style>

<?php
// Include footer
include_once 'includes/footer.php';
?>
