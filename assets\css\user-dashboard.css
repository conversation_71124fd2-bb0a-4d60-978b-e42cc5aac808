/* User Dashboard Styles */

/* Add padding to container to prevent header overlap */
.container {
    padding-top: 120px;
}

/* Dashboard header styles */
.dashboard-header {
    margin-bottom: 30px;
}

.dashboard-header h1 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

/* Page header styles */
.page-header {
    margin-bottom: 30px;
}

.page-header h1 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

/* Glass card styles */
.glass-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 30px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

/* Row and column layout */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding: 0 15px;
}

.col-md-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
    padding: 0 15px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    /* Adjust padding for mobile */
    .container {
        padding-top: 100px;
    }

    .row {
        flex-direction: column;
    }

    .col-md-4, .col-md-8 {
        max-width: 100%;
        flex: 0 0 100%;
    }

    /* Improve spacing in mobile view */
    .dashboard-header, .page-header {
        margin-bottom: 25px;
    }
}

/* Dark theme adjustments */
.dark-theme .glass-card {
    background-color: rgba(30, 30, 40, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
}
