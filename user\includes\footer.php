    <!-- Theme Toggle and Mobile Menu Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Theme Toggle Functionality
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                const body = document.body;
                const icon = themeToggle.querySelector('i');

                // Toggle theme on button click
                themeToggle.addEventListener('click', function() {
                    body.classList.toggle('dark-theme');

                    // Update icon
                    if (body.classList.contains('dark-theme')) {
                        icon.classList.replace('fa-moon', 'fa-sun');
                        document.cookie = "theme=dark; path=/; max-age=31536000"; // 1 year
                    } else {
                        icon.classList.replace('fa-sun', 'fa-moon');
                        document.cookie = "theme=light; path=/; max-age=31536000"; // 1 year
                    }
                });
            }

            // Mobile Menu Functionality
            const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
            if (mobileMenuToggle) {
                const nav = document.getElementById('main-nav');

                // Toggle mobile menu
                mobileMenuToggle.addEventListener('click', function() {
                    nav.classList.toggle('active');
                    body.classList.toggle('menu-active');

                    // Toggle menu icon between bars and X
                    const menuIcon = mobileMenuToggle.querySelector('i');
                    if (nav.classList.contains('active')) {
                        menuIcon.classList.replace('fa-bars', 'fa-times');
                    } else {
                        menuIcon.classList.replace('fa-times', 'fa-bars');
                    }
                });

                // Close mobile menu when clicking outside
                document.addEventListener('click', function(event) {
                    const isClickInsideNav = nav.contains(event.target);
                    const isClickOnToggle = mobileMenuToggle.contains(event.target);

                    if (!isClickInsideNav && !isClickOnToggle && nav.classList.contains('active')) {
                        nav.classList.remove('active');
                        body.classList.remove('menu-active');
                        mobileMenuToggle.querySelector('i').classList.replace('fa-times', 'fa-bars');
                    }
                });

                // Close mobile menu when window is resized to desktop size
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 992 && nav.classList.contains('active')) {
                        nav.classList.remove('active');
                        body.classList.remove('menu-active');
                        mobileMenuToggle.querySelector('i').classList.replace('fa-times', 'fa-bars');
                    }
                });
            }
        });
    </script>

    <!-- Notification System -->
    <script src="<?php echo SITE_URL; ?>/assets/js/notifications.js"></script>
    <?php displayNotifications(); ?>

    <!-- Logo Generator -->
    <script src="<?php echo SITE_URL; ?>/assets/js/logo-generator.js"></script>

    <!-- Mobile Enhancements -->
    <script src="<?php echo SITE_URL; ?>/assets/js/mobile-enhancements.js"></script>

    <?php if(isset($additionalJs)): ?>
        <?php foreach($additionalJs as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
</body>
</html>