<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once '../includes/geocoding.php';

// Check if user is logged in
if(!isLoggedIn()) {
    setMessage('Please login to access the dashboard', 'error');
    redirect('../index.php');
}

// Process form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add/Edit Shipment
    if (isset($_POST['action']) && ($_POST['action'] === 'add' || $_POST['action'] === 'edit')) {
        // Basic shipment info
        $trackingNumber = sanitize($_POST['tracking_number']);
        $customerName = sanitize($_POST['customer_name']);
        $origin = sanitize($_POST['origin']);
        $destination = sanitize($_POST['destination']);
        $status = sanitize($_POST['status']);
        $estimatedDelivery = sanitize($_POST['estimated_delivery']);
        $shipmentId = isset($_POST['shipment_id']) ? (int)$_POST['shipment_id'] : 0;

        // Shipment info
        $shipmentName = isset($_POST['shipment_name']) ? sanitize($_POST['shipment_name']) : null;
        $shipmentDescription = isset($_POST['shipment_description']) ? sanitize($_POST['shipment_description']) : null;

        // Shopper info
        $shopperName = isset($_POST['shopper_name']) ? sanitize($_POST['shopper_name']) : null;
        $shopperEmail = isset($_POST['shopper_email']) ? sanitize($_POST['shopper_email']) : null;
        $shopperPhone = isset($_POST['shopper_phone']) ? sanitize($_POST['shopper_phone']) : null;
        $shopperAddress = isset($_POST['shopper_address']) ? sanitize($_POST['shopper_address']) : null;

        // Receiver info
        $receiverName = isset($_POST['receiver_name']) ? sanitize($_POST['receiver_name']) : null;
        $receiverEmail = isset($_POST['receiver_email']) ? sanitize($_POST['receiver_email']) : null;
        $receiverPhone = isset($_POST['receiver_phone']) ? sanitize($_POST['receiver_phone']) : null;
        $receiverAddress = isset($_POST['receiver_address']) ? sanitize($_POST['receiver_address']) : null;

        // Package info
        $packageName = isset($_POST['package_name']) ? sanitize($_POST['package_name']) : null;
        $packageDescription = isset($_POST['package_description']) ? sanitize($_POST['package_description']) : null;
        $packageWeight = isset($_POST['package_weight']) && !empty($_POST['package_weight']) ? (float)$_POST['package_weight'] : null;
        $packageDimensions = isset($_POST['package_dimensions']) ? sanitize($_POST['package_dimensions']) : null;
        $shippingService = isset($_POST['shipping_service']) ? sanitize($_POST['shipping_service']) : null;
        $shippingCost = isset($_POST['shipping_cost']) && !empty($_POST['shipping_cost']) ? (float)$_POST['shipping_cost'] : null;

        // Handle package picture upload
        $packagePicture = null;
        if (isset($_FILES['package_picture']) && $_FILES['package_picture']['error'] === UPLOAD_ERR_OK) {
            $uploadDir = '../uploads/packages/';

            // Create directory if it doesn't exist
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Get file info
            $fileName = $_FILES['package_picture']['name'];
            $fileSize = $_FILES['package_picture']['size'];
            $fileTmpName = $_FILES['package_picture']['tmp_name'];
            $fileType = $_FILES['package_picture']['type'];
            $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

            // Check if file is an image
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
            if (in_array($fileExtension, $allowedExtensions)) {
                // Check file size (max 2MB)
                if ($fileSize <= 2 * 1024 * 1024) {
                    // Generate unique filename
                    $newFileName = $trackingNumber . '_' . time() . '.' . $fileExtension;
                    $uploadPath = $uploadDir . $newFileName;

                    // Move uploaded file
                    if (move_uploaded_file($fileTmpName, $uploadPath)) {
                        $packagePicture = $newFileName;
                    } else {
                        setErrorNotification('Error uploading package picture. Please try again.', 10000);
                    }
                } else {
                    setErrorNotification('Package picture is too large. Maximum size is 2MB.', 10000);
                }
            } else {
                setErrorNotification('Invalid file type. Only JPG, JPEG, PNG, and GIF files are allowed.', 10000);
            }
        }

        // Validate input
        if (empty($trackingNumber) || empty($customerName) || empty($origin) || empty($destination) || empty($status)) {
            setMessage('All required fields must be filled', 'error');
        } else {
            if ($_POST['action'] === 'add') {
                // Check if tracking number already exists
                $db->query("SELECT id FROM shipments WHERE tracking_number = :tracking_number");
                $db->bind(':tracking_number', $trackingNumber);
                $existingShipment = $db->single();

                if ($existingShipment) {
                    setMessage('Tracking number already exists', 'error');
                } else {
                    // Add new shipment with all fields
                    $db->query("INSERT INTO shipments (
                                tracking_number, customer_name, origin, destination, status, estimated_delivery,
                                shipment_name, shipment_description,
                                shopper_name, shopper_email, shopper_phone, shopper_address,
                                receiver_name, receiver_email, receiver_phone, receiver_address,
                                package_name, package_description, package_weight, package_dimensions,
                                shipping_service, shipping_cost, package_picture
                                ) VALUES (
                                :tracking_number, :customer_name, :origin, :destination, :status, :estimated_delivery,
                                :shipment_name, :shipment_description,
                                :shopper_name, :shopper_email, :shopper_phone, :shopper_address,
                                :receiver_name, :receiver_email, :receiver_phone, :receiver_address,
                                :package_name, :package_description, :package_weight, :package_dimensions,
                                :shipping_service, :shipping_cost, :package_picture
                                )");

                    // Bind basic shipment parameters
                    $db->bind(':tracking_number', $trackingNumber);
                    $db->bind(':customer_name', $customerName);
                    $db->bind(':origin', $origin);
                    $db->bind(':destination', $destination);
                    $db->bind(':status', $status);
                    $db->bind(':estimated_delivery', $estimatedDelivery);

                    // Bind shipment name and description
                    $db->bind(':shipment_name', $shipmentName);
                    $db->bind(':shipment_description', $shipmentDescription);

                    // Bind shopper parameters
                    $db->bind(':shopper_name', $shopperName);
                    $db->bind(':shopper_email', $shopperEmail);
                    $db->bind(':shopper_phone', $shopperPhone);
                    $db->bind(':shopper_address', $shopperAddress);

                    // Bind receiver parameters
                    $db->bind(':receiver_name', $receiverName);
                    $db->bind(':receiver_email', $receiverEmail);
                    $db->bind(':receiver_phone', $receiverPhone);
                    $db->bind(':receiver_address', $receiverAddress);

                    // Bind package parameters
                    $db->bind(':package_name', $packageName);
                    $db->bind(':package_description', $packageDescription);
                    $db->bind(':package_weight', $packageWeight);
                    $db->bind(':package_dimensions', $packageDimensions);
                    $db->bind(':shipping_service', $shippingService);
                    $db->bind(':shipping_cost', $shippingCost);
                    $db->bind(':package_picture', $packagePicture);

                    if ($db->execute()) {
                        $newShipmentId = $db->lastInsertId();

                        // Send email notification if email notifications are enabled
                        if (getSetting('enable_email_notifications', '1') === '1') {
                            // Check if we have an email to send to
                            $customerEmail = null;

                            // Try to get email from receiver_email first
                            if (!empty($receiverEmail)) {
                                $customerEmail = $receiverEmail;
                            }
                            // If no receiver email, try shopper_email
                            else if (!empty($shopperEmail)) {
                                $customerEmail = $shopperEmail;
                            }

                            // If we have an email, send the notification
                            if ($customerEmail && filter_var($customerEmail, FILTER_VALIDATE_EMAIL)) {
                                try {
                                    // Get the full shipment data
                                    $db->query("SELECT * FROM shipments WHERE id = :id");
                                    $db->bind(':id', $newShipmentId);
                                    $newShipment = $db->single();

                                    sendNewShipmentEmail($newShipment, $customerEmail);
                                    // Log successful email
                                    debugLog("New shipment email sent for shipment #{$newShipmentId} to {$customerEmail}");
                                } catch (Exception $e) {
                                    // Log email error but don't affect the shipment creation result
                                    debugLog("Error sending new shipment email for shipment #{$newShipmentId}: " . $e->getMessage());
                                }
                            }
                        }

                        setSuccessNotification('Shipment added successfully', 7000);
                    } else {
                        setErrorNotification('Error adding shipment. Please try again.', 10000);
                    }
                }
            } else {
                // Edit existing shipment
                $updateQuery = "UPDATE shipments SET
                            tracking_number = :tracking_number,
                            customer_name = :customer_name,
                            origin = :origin,
                            destination = :destination,
                            status = :status,
                            estimated_delivery = :estimated_delivery,
                            shipment_name = :shipment_name,
                            shipment_description = :shipment_description,
                            shopper_name = :shopper_name,
                            shopper_email = :shopper_email,
                            shopper_phone = :shopper_phone,
                            shopper_address = :shopper_address,
                            receiver_name = :receiver_name,
                            receiver_email = :receiver_email,
                            receiver_phone = :receiver_phone,
                            receiver_address = :receiver_address,
                            package_name = :package_name,
                            package_description = :package_description,
                            package_weight = :package_weight,
                            package_dimensions = :package_dimensions,
                            shipping_service = :shipping_service,
                            shipping_cost = :shipping_cost";

                // Add package picture to update query if a new one was uploaded
                if ($packagePicture) {
                    $updateQuery .= ", package_picture = :package_picture";
                }

                $updateQuery .= " WHERE id = :id";

                $db->query($updateQuery);

                // Bind basic shipment parameters
                $db->bind(':tracking_number', $trackingNumber);
                $db->bind(':customer_name', $customerName);
                $db->bind(':origin', $origin);
                $db->bind(':destination', $destination);
                $db->bind(':status', $status);
                $db->bind(':estimated_delivery', $estimatedDelivery);

                // Bind shipment name and description
                $db->bind(':shipment_name', $shipmentName);
                $db->bind(':shipment_description', $shipmentDescription);

                // Bind shopper parameters
                $db->bind(':shopper_name', $shopperName);
                $db->bind(':shopper_email', $shopperEmail);
                $db->bind(':shopper_phone', $shopperPhone);
                $db->bind(':shopper_address', $shopperAddress);

                // Bind receiver parameters
                $db->bind(':receiver_name', $receiverName);
                $db->bind(':receiver_email', $receiverEmail);
                $db->bind(':receiver_phone', $receiverPhone);
                $db->bind(':receiver_address', $receiverAddress);

                // Bind package parameters
                $db->bind(':package_name', $packageName);
                $db->bind(':package_description', $packageDescription);
                $db->bind(':package_weight', $packageWeight);
                $db->bind(':package_dimensions', $packageDimensions);
                $db->bind(':shipping_service', $shippingService);
                $db->bind(':shipping_cost', $shippingCost);

                // Bind package picture if a new one was uploaded
                if ($packagePicture) {
                    $db->bind(':package_picture', $packagePicture);
                }

                $db->bind(':id', $shipmentId);

                if ($db->execute()) {
                    // Update delivered_at date if status is delivered
                    if ($status === 'delivered') {
                        $db->query("UPDATE shipments SET delivered_at = NOW() WHERE id = :id AND delivered_at IS NULL");
                        $db->bind(':id', $shipmentId);
                        $db->execute();
                    }

                    setSuccessNotification('Shipment #' . $shipmentId . ' updated successfully', 7000);
                } else {
                    setErrorNotification('Error updating shipment. Please try again.', 10000);
                }
            }
        }
    }

    // Delete Shipment
    if (isset($_POST['action']) && $_POST['action'] === 'delete') {
        $shipmentId = (int)$_POST['shipment_id'];

        $db->query("DELETE FROM shipments WHERE id = :id");
        $db->bind(':id', $shipmentId);

        if ($db->execute()) {
            setSuccessNotification('Shipment #' . $shipmentId . ' deleted successfully', 7000);
        } else {
            setErrorNotification('Error deleting shipment. Please try again.', 10000);
        }
    }

    // Add Tracking Update
    if (isset($_POST['action']) && $_POST['action'] === 'add_update') {
        $shipmentId = (int)$_POST['shipment_id'];
        $location = sanitize($_POST['location']);
        $status = sanitize($_POST['status']);
        $notes = sanitize($_POST['notes']);
        $latitude = !empty($_POST['latitude']) ? (float)$_POST['latitude'] : null;
        $longitude = !empty($_POST['longitude']) ? (float)$_POST['longitude'] : null;

        // If coordinates are not provided but geocoding is enabled, try to geocode the location
        if (($latitude === null || $longitude === null) && getSetting('enable_geocoding', '1') === '1') {
            $geocoder = new GeocodingHelper();
            $geocodeResult = $geocoder->geocode($location);
            if ($geocodeResult) {
                $latitude = $geocodeResult['latitude'];
                $longitude = $geocodeResult['longitude'];
            }
        }

        if (empty($location) || empty($status)) {
            setMessage('Location and status are required', 'error');
        } else {
            // Add tracking update
            $result = addTrackingUpdate($shipmentId, $location, $status, $latitude, $longitude, $notes);

            if ($result) {
                // Get the shipment data for email notification
                $db->query("SELECT * FROM shipments WHERE id = :id");
                $db->bind(':id', $shipmentId);
                $shipment = $db->single();

                // Update shipment status
                updateShipmentStatus($shipmentId, $status);

                // Update delivered_at date if status is delivered
                if ($status === 'delivered') {
                    $db->query("UPDATE shipments SET delivered_at = NOW() WHERE id = :id AND delivered_at IS NULL");
                    $db->bind(':id', $shipmentId);
                    $db->execute();
                }

                // Send email notification if email notifications are enabled
                if (getSetting('enable_email_notifications', '1') === '1' && $shipment) {
                    // Check if we have an email to send to
                    $customerEmail = null;

                    // Try to get email from receiver_email first
                    if (!empty($shipment['receiver_email'])) {
                        $customerEmail = $shipment['receiver_email'];
                    }
                    // If no receiver email, try shopper_email
                    else if (!empty($shipment['shopper_email'])) {
                        $customerEmail = $shipment['shopper_email'];
                    }

                    // If we have an email, send the notification
                    if ($customerEmail && filter_var($customerEmail, FILTER_VALIDATE_EMAIL)) {
                        try {
                            sendShipmentStatusUpdateEmail($shipment, $status, $customerEmail);
                            // Log successful email
                            debugLog("Status update email sent for shipment #{$shipmentId} to {$customerEmail}");
                        } catch (Exception $e) {
                            // Log email error but don't affect the tracking update result
                            debugLog("Error sending status update email for shipment #{$shipmentId}: " . $e->getMessage());
                        }
                    }
                }

                setMessage('Tracking update added successfully', 'success');
            } else {
                setMessage('Error adding tracking update', 'error');
            }
        }
    }

    // Redirect to prevent form resubmission
    redirect('manage-shipments.php');
}

// Get shipments with pagination and filtering
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Filter parameters
$statusFilter = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$searchTerm = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build query conditions
$conditions = [];
$params = [];

if (!empty($statusFilter)) {
    $conditions[] = "status = :status";
    $params[':status'] = $statusFilter;
}

if (!empty($searchTerm)) {
    $conditions[] = "(tracking_number LIKE :search OR customer_name LIKE :search OR origin LIKE :search OR destination LIKE :search)";
    $params[':search'] = "%$searchTerm%";
}

$whereClause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";

// Count total shipments
$countQuery = "SELECT COUNT(*) as total FROM shipments $whereClause";
$db->query($countQuery);
foreach ($params as $param => $value) {
    $db->bind($param, $value);
}
$totalShipments = $db->single()['total'];
$totalPages = ceil($totalShipments / $perPage);

// Get shipments
$shipmentsQuery = "SELECT * FROM shipments $whereClause ORDER BY created_at DESC LIMIT :offset, :per_page";
$db->query($shipmentsQuery);
foreach ($params as $param => $value) {
    $db->bind($param, $value);
}
$db->bind(':offset', $offset, PDO::PARAM_INT);
$db->bind(':per_page', $perPage, PDO::PARAM_INT);
$shipments = $db->resultSet();

// Include header
include_once '../includes/header.php';
?>

<section class="admin-section" style="padding-top: 120px;">
    <div class="container">
        <div class="admin-header">
            <h1><i class="fas fa-shipping-fast"></i> Manage Shipments</h1>
            <div class="admin-actions">
                <button class="btn add-btn" data-toggle="modal" data-target="#addShipmentModal"><i class="fas fa-plus"></i> Add Shipment</button>
                <a href="index.php" class="btn back-btn"><i class="fas fa-arrow-left"></i> Back to Dashboard</a>
            </div>
        </div>

        <?php displayMessage(); ?>

        <div class="admin-content">
            <div class="card">
                <div class="card-header">
                    <h2>Shipments</h2>
                    <div class="card-actions">
                        <form action="manage-shipments.php" method="GET" class="filter-form">
                            <div class="form-group">
                                <select name="status" onchange="this.form.submit()">
                                    <option value="">All Statuses</option>
                                    <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="in_transit" <?php echo $statusFilter === 'in_transit' ? 'selected' : ''; ?>>In Transit</option>
                                    <option value="delivered" <?php echo $statusFilter === 'delivered' ? 'selected' : ''; ?>>Delivered</option>
                                    <option value="delayed" <?php echo $statusFilter === 'delayed' ? 'selected' : ''; ?>>Delayed</option>
                                    <option value="cancelled" <?php echo $statusFilter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                </select>
                            </div>
                            <div class="search-box">
                                <input type="text" name="search" placeholder="Search shipments..." value="<?php echo $searchTerm; ?>">
                                <button type="submit"><i class="fas fa-search"></i></button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($shipments)): ?>
                        <div class="empty-state">
                            <i class="fas fa-box-open"></i>
                            <p>No shipments found</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table mobile-card-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Tracking #</th>
                                        <th>Customer</th>
                                        <th>Route</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Est. Delivery</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($shipments as $shipment): ?>
                                        <tr>
                                            <td><?php echo $shipment['id']; ?></td>
                                            <td><a href="../tracking/index.php?tracking_number=<?php echo $shipment['tracking_number']; ?>" class="tracking-link" target="_blank"><i class="fas fa-search-location"></i> <?php echo $shipment['tracking_number']; ?></a></td>
                                            <td><?php echo $shipment['customer_name']; ?></td>
                                            <td><?php echo $shipment['origin']; ?> to <?php echo $shipment['destination']; ?></td>
                                            <td>
                                                <span class="badge <?php echo getStatusClass($shipment['status']); ?>">
                                                    <?php echo ucfirst(str_replace('_', ' ', $shipment['status'])); ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatDate($shipment['created_at']); ?></td>
                                            <td><?php echo !empty($shipment['estimated_delivery']) ? date('M j, Y', strtotime($shipment['estimated_delivery'])) : 'N/A'; ?></td>
                                            <td class="actions">
                                                <button class="btn-icon view-btn" data-toggle="modal" data-target="#viewShipmentModal" data-id="<?php echo $shipment['id']; ?>">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn-icon edit-btn" data-toggle="modal" data-target="#editShipmentModal"
                                                        data-id="<?php echo $shipment['id']; ?>"
                                                        data-tracking="<?php echo $shipment['tracking_number']; ?>"
                                                        data-customer="<?php echo $shipment['customer_name']; ?>"
                                                        data-origin="<?php echo $shipment['origin']; ?>"
                                                        data-destination="<?php echo $shipment['destination']; ?>"
                                                        data-status="<?php echo $shipment['status']; ?>"
                                                        data-delivery="<?php echo $shipment['estimated_delivery']; ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn-icon update-btn" data-toggle="modal" data-target="#addUpdateModal" data-id="<?php echo $shipment['id']; ?>">
                                                    <i class="fas fa-plus-circle"></i>
                                                </button>
                                                <button class="btn-icon delete-btn" data-toggle="modal" data-target="#deleteShipmentModal" data-id="<?php echo $shipment['id']; ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <div class="pagination">
                                <?php if ($page > 1): ?>
                                    <a href="?page=<?php echo $page - 1; ?>&status=<?php echo $statusFilter; ?>&search=<?php echo $searchTerm; ?>" class="page-link"><i class="fas fa-chevron-left"></i></a>
                                <?php endif; ?>

                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <a href="?page=<?php echo $i; ?>&status=<?php echo $statusFilter; ?>&search=<?php echo $searchTerm; ?>" class="page-link <?php echo $i === $page ? 'active' : ''; ?>"><?php echo $i; ?></a>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <a href="?page=<?php echo $page + 1; ?>&status=<?php echo $statusFilter; ?>&search=<?php echo $searchTerm; ?>" class="page-link"><i class="fas fa-chevron-right"></i></a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Add Shipment Modal -->
<div class="modal" id="addShipmentModal">
    <div class="modal-content modal-lg">
        <div class="modal-header">
            <h2>Add Shipment</h2>
            <button class="close-modal">&times;</button>
        </div>
        <div class="modal-body">
            <form action="manage-shipments.php" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" value="add">

                <div class="form-tabs">
                    <button type="button" class="tab-btn active" data-tab="basic-info">Basic Info</button>
                    <button type="button" class="tab-btn" data-tab="shopper-info">Shopper Info</button>
                    <button type="button" class="tab-btn" data-tab="receiver-info">Receiver Info</button>
                    <button type="button" class="tab-btn" data-tab="package-info">Package Info</button>
                </div>

                <div class="tab-content active" id="basic-info">
                    <h3>Basic Information</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="tracking_number">Tracking Number</label>
                            <input type="text" id="tracking_number" name="tracking_number" value="<?php echo generateTrackingNumber(); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="customer_name">Customer Name</label>
                            <input type="text" id="customer_name" name="customer_name" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="origin">Origin</label>
                            <input type="text" id="origin" name="origin" required>
                        </div>

                        <div class="form-group">
                            <label for="destination">Destination</label>
                            <input type="text" id="destination" name="destination" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" name="status" required>
                                <option value="pending">Pending</option>
                                <option value="picked_up">Picked Up</option>
                                <option value="in_transit">In Transit</option>
                                <option value="arrived_at_facility">Arrived at Facility</option>
                                <option value="out_for_delivery">Out for Delivery</option>
                                <option value="delivered">Delivered</option>
                                <option value="delayed">Delayed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="estimated_delivery">Estimated Delivery Date</label>
                            <input type="date" id="estimated_delivery" name="estimated_delivery" required>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="shopper-info">
                    <h3>Shipment Information</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="shipment_name">Shipment Name</label>
                            <input type="text" id="shipment_name" name="shipment_name">
                        </div>

                        <div class="form-group">
                            <label for="shipment_description">Shipment Description</label>
                            <textarea id="shipment_description" name="shipment_description" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="shopper_phone">Shopper Phone</label>
                            <input type="text" id="shopper_phone" name="shopper_phone">
                        </div>

                        <div class="form-group">
                            <label for="shopper_address">Shopper Address</label>
                            <textarea id="shopper_address" name="shopper_address" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="receiver-info">
                    <h3>Receiver Information</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="receiver_name">Receiver Name</label>
                            <input type="text" id="receiver_name" name="receiver_name">
                        </div>

                        <div class="form-group">
                            <label for="receiver_email">Receiver Email</label>
                            <input type="email" id="receiver_email" name="receiver_email">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="receiver_phone">Receiver Phone</label>
                            <input type="text" id="receiver_phone" name="receiver_phone">
                        </div>

                        <div class="form-group">
                            <label for="receiver_address">Receiver Address</label>
                            <textarea id="receiver_address" name="receiver_address" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="package-info">
                    <h3>Package Information</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="package_name">Package Name</label>
                            <input type="text" id="package_name" name="package_name">
                        </div>

                        <div class="form-group">
                            <label for="package_description">Package Description</label>
                            <textarea id="package_description" name="package_description" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="package_weight">Package Weight (kg)</label>
                            <input type="number" step="0.01" id="package_weight" name="package_weight">
                        </div>

                        <div class="form-group">
                            <label for="package_dimensions">Package Dimensions (LxWxH cm)</label>
                            <input type="text" id="package_dimensions" name="package_dimensions" placeholder="e.g. 30x20x15">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="shipping_service">Shipping Service</label>
                            <select id="shipping_service" name="shipping_service">
                                <option value="">Select a service</option>
                                <option value="Standard Shipping">Standard Shipping</option>
                                <option value="Express Shipping">Express Shipping</option>
                                <option value="Priority Shipping">Priority Shipping</option>
                                <option value="Economy Shipping">Economy Shipping</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="shipping_cost">Shipping Cost</label>
                            <input type="number" step="0.01" id="shipping_cost" name="shipping_cost">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="package_picture">Package Picture</label>
                        <input type="file" id="package_picture" name="package_picture" accept="image/*">
                        <small class="form-text">Upload a picture of the package (max 2MB, JPG/PNG only)</small>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn cancel-btn" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn submit-btn">Add Shipment</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Shipment Modal -->
<div class="modal" id="editShipmentModal">
    <div class="modal-content modal-lg">
        <div class="modal-header">
            <h2>Edit Shipment</h2>
            <button class="close-modal">&times;</button>
        </div>
        <div class="modal-body">
            <form action="manage-shipments.php" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="shipment_id" id="edit_shipment_id">

                <div class="form-tabs">
                    <button type="button" class="tab-btn active" data-tab="edit-basic-info">Basic Info</button>
                    <button type="button" class="tab-btn" data-tab="edit-shopper-info">Shopper Info</button>
                    <button type="button" class="tab-btn" data-tab="edit-receiver-info">Receiver Info</button>
                    <button type="button" class="tab-btn" data-tab="edit-package-info">Package Info</button>
                </div>

                <div class="tab-content active" id="edit-basic-info">
                    <h3>Basic Information</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_tracking_number">Tracking Number</label>
                            <input type="text" id="edit_tracking_number" name="tracking_number" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_customer_name">Customer Name</label>
                            <input type="text" id="edit_customer_name" name="customer_name" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_origin">Origin</label>
                            <input type="text" id="edit_origin" name="origin" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_destination">Destination</label>
                            <input type="text" id="edit_destination" name="destination" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_status">Status</label>
                            <select id="edit_status" name="status" required>
                                <option value="pending">Pending</option>
                                <option value="picked_up">Picked Up</option>
                                <option value="in_transit">In Transit</option>
                                <option value="arrived_at_facility">Arrived at Facility</option>
                                <option value="out_for_delivery">Out for Delivery</option>
                                <option value="delivered">Delivered</option>
                                <option value="delayed">Delayed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_estimated_delivery">Estimated Delivery Date</label>
                            <input type="date" id="edit_estimated_delivery" name="estimated_delivery" required>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="edit-shopper-info">
                    <h3>Shipment Information</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_shipment_name">Shipment Name</label>
                            <input type="text" id="edit_shipment_name" name="shipment_name">
                        </div>

                        <div class="form-group">
                            <label for="edit_shipment_description">Shipment Description</label>
                            <textarea id="edit_shipment_description" name="shipment_description" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_shopper_phone">Shopper Phone</label>
                            <input type="text" id="edit_shopper_phone" name="shopper_phone">
                        </div>

                        <div class="form-group">
                            <label for="edit_shopper_address">Shopper Address</label>
                            <textarea id="edit_shopper_address" name="shopper_address" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="edit-receiver-info">
                    <h3>Receiver Information</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_receiver_name">Receiver Name</label>
                            <input type="text" id="edit_receiver_name" name="receiver_name">
                        </div>

                        <div class="form-group">
                            <label for="edit_receiver_email">Receiver Email</label>
                            <input type="email" id="edit_receiver_email" name="receiver_email">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_receiver_phone">Receiver Phone</label>
                            <input type="text" id="edit_receiver_phone" name="receiver_phone">
                        </div>

                        <div class="form-group">
                            <label for="edit_receiver_address">Receiver Address</label>
                            <textarea id="edit_receiver_address" name="receiver_address" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <div class="tab-content" id="edit-package-info">
                    <h3>Package Information</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_package_name">Package Name</label>
                            <input type="text" id="edit_package_name" name="package_name">
                        </div>

                        <div class="form-group">
                            <label for="edit_package_description">Package Description</label>
                            <textarea id="edit_package_description" name="package_description" rows="3"></textarea>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_package_weight">Package Weight (kg)</label>
                            <input type="number" step="0.01" id="edit_package_weight" name="package_weight">
                        </div>

                        <div class="form-group">
                            <label for="edit_package_dimensions">Package Dimensions (LxWxH cm)</label>
                            <input type="text" id="edit_package_dimensions" name="package_dimensions" placeholder="e.g. 30x20x15">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="edit_shipping_service">Shipping Service</label>
                            <select id="edit_shipping_service" name="shipping_service">
                                <option value="">Select a service</option>
                                <option value="Standard Shipping">Standard Shipping</option>
                                <option value="Express Shipping">Express Shipping</option>
                                <option value="Priority Shipping">Priority Shipping</option>
                                <option value="Economy Shipping">Economy Shipping</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="edit_shipping_cost">Shipping Cost</label>
                            <input type="number" step="0.01" id="edit_shipping_cost" name="shipping_cost">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="edit_package_picture">Package Picture</label>
                        <div id="current_package_picture" class="mb-2"></div>
                        <input type="file" id="edit_package_picture" name="package_picture" accept="image/*">
                        <small class="form-text">Upload a new picture of the package (max 2MB, JPG/PNG only)</small>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn cancel-btn" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn submit-btn">Update Shipment</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Shipment Modal -->
<div class="modal" id="deleteShipmentModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Delete Shipment</h2>
            <button class="close-modal">&times;</button>
        </div>
        <div class="modal-body">
            <p>Are you sure you want to delete this shipment? This action cannot be undone.</p>
            <form action="manage-shipments.php" method="POST">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="shipment_id" id="delete_shipment_id">

                <div class="form-actions">
                    <button type="button" class="btn cancel-btn" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn delete-btn">Delete Shipment</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Tracking Update Modal -->
<div class="modal" id="addUpdateModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Add Tracking Update</h2>
            <button class="close-modal">&times;</button>
        </div>
        <div class="modal-body">
            <form action="manage-shipments.php" method="POST">
                <input type="hidden" name="action" value="add_update">
                <input type="hidden" name="shipment_id" id="update_shipment_id">

                <div class="form-group">
                    <label for="location">Location</label>
                    <div class="input-with-buttons">
                        <input type="text" id="location" name="location" required>
                        <button type="button" id="geocodeBtn" class="btn outline-btn"><i class="fas fa-search-location"></i></button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="update_status">Status</label>
                    <select id="update_status" name="status" required>
                        <option value="pending">Pending</option>
                        <option value="picked_up">Picked Up</option>
                        <option value="in_transit">In Transit</option>
                        <option value="arrived_at_facility">Arrived at Facility</option>
                        <option value="out_for_delivery">Out for Delivery</option>
                        <option value="delivered">Delivered</option>
                        <option value="delayed">Delayed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="notes">Notes</label>
                    <textarea id="notes" name="notes" rows="3"></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group half">
                        <label for="latitude">Latitude (optional)</label>
                        <input type="text" id="latitude" name="latitude" placeholder="e.g. 40.7128">
                    </div>

                    <div class="form-group half">
                        <label for="longitude">Longitude (optional)</label>
                        <input type="text" id="longitude" name="longitude" placeholder="e.g. -74.0060">
                    </div>
                </div>

                <div class="form-group">
                    <div id="location-map" style="height: 300px; margin-top: 10px; display: none; border-radius: 8px; overflow: hidden;"></div>
                    <div class="map-controls" style="margin-top: 10px;">
                        <button type="button" id="toggleMapBtn" class="btn outline-btn"><i class="fas fa-map"></i> Toggle Map</button>
                        <button type="button" id="getCurrentLocationBtn" class="btn outline-btn"><i class="fas fa-map-marker-alt"></i> Get Current Location</button>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn cancel-btn" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn submit-btn">Add Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Shipment Modal -->
<div class="modal" id="viewShipmentModal">
    <div class="modal-content modal-lg">
        <div class="modal-header">
            <h2>Shipment Details</h2>
            <button class="close-modal">&times;</button>
        </div>
        <div class="modal-body">
            <div id="shipmentDetails">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i> Loading...
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load Leaflet CSS if not already loaded
    if (!document.querySelector('link[href*="leaflet.css"]')) {
        const leafletCss = document.createElement('link');
        leafletCss.rel = 'stylesheet';
        leafletCss.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
        document.head.appendChild(leafletCss);
    }

    // Load Leaflet JS if not already loaded
    if (typeof L === 'undefined') {
        const leafletScript = document.createElement('script');
        leafletScript.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
        document.head.appendChild(leafletScript);
    }

    // Modal functionality
    const modals = document.querySelectorAll('.modal');
    const modalTriggers = document.querySelectorAll('[data-toggle="modal"]');
    const modalClosers = document.querySelectorAll('.close-modal, .cancel-btn, [data-dismiss="modal"]');

    // Open modal
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function() {
            const modalId = this.getAttribute('data-target');
            const modal = document.querySelector(modalId);

            if (modal) {
                modal.style.display = 'block';

                // If it's an edit button, populate the form
                if (this.classList.contains('edit-btn')) {
                    document.getElementById('edit_shipment_id').value = this.getAttribute('data-id');
                    document.getElementById('edit_tracking_number').value = this.getAttribute('data-tracking');
                    document.getElementById('edit_customer_name').value = this.getAttribute('data-customer');
                    document.getElementById('edit_origin').value = this.getAttribute('data-origin');
                    document.getElementById('edit_destination').value = this.getAttribute('data-destination');
                    document.getElementById('edit_status').value = this.getAttribute('data-status');
                    document.getElementById('edit_estimated_delivery').value = this.getAttribute('data-delivery');
                }

                // If it's a delete button, set the shipment ID
                if (this.classList.contains('delete-btn')) {
                    document.getElementById('delete_shipment_id').value = this.getAttribute('data-id');
                }

                // If it's an update button, set the shipment ID
                if (this.classList.contains('update-btn')) {
                    document.getElementById('update_shipment_id').value = this.getAttribute('data-id');
                }

                // If it's a view button, load shipment details
                if (this.classList.contains('view-btn')) {
                    const shipmentId = this.getAttribute('data-id');
                    loadShipmentDetails(shipmentId);
                }
            }
        });
    });

    // Close modal
    modalClosers.forEach(closer => {
        closer.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
            }
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        modals.forEach(modal => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    });

    // Set default estimated delivery date to 7 days from now
    const estimatedDeliveryInput = document.getElementById('estimated_delivery');
    if (estimatedDeliveryInput) {
        const date = new Date();
        date.setDate(date.getDate() + 7);
        estimatedDeliveryInput.value = date.toISOString().split('T')[0];
    }

    // Function to load shipment details
    function loadShipmentDetails(shipmentId) {
        const detailsContainer = document.getElementById('shipmentDetails');
        detailsContainer.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';

        // Fetch shipment details using AJAX
        fetch(`get-shipment-details.php?id=${shipmentId}`)
            .then(response => response.text())
            .then(data => {
                detailsContainer.innerHTML = data;
            })
            .catch(error => {
                detailsContainer.innerHTML = `<div class="error">Error loading shipment details: ${error.message}</div>`;
            });
    }

    // Geocoding and map functionality for tracking updates
    const locationInput = document.getElementById('location');
    const latitudeInput = document.getElementById('latitude');
    const longitudeInput = document.getElementById('longitude');
    const geocodeBtn = document.getElementById('geocodeBtn');
    const toggleMapBtn = document.getElementById('toggleMapBtn');
    const getCurrentLocationBtn = document.getElementById('getCurrentLocationBtn');
    const mapContainer = document.getElementById('location-map');

    let locationMap = null;
    let locationMarker = null;

    // Initialize map when visible
    function initMap() {
        if (!locationMap) {
            // Wait for Leaflet to be loaded
            if (typeof L === 'undefined') {
                setTimeout(initMap, 100);
                return;
            }

            // Get initial coordinates
            let initialLat = 40.7128;
            let initialLng = -74.0060;
            let initialZoom = 2;

            // Use coordinates from inputs if available
            if (latitudeInput.value && longitudeInput.value) {
                initialLat = parseFloat(latitudeInput.value);
                initialLng = parseFloat(longitudeInput.value);
                initialZoom = 13;
            }

            // Create map
            locationMap = L.map('location-map').setView([initialLat, initialLng], initialZoom);

            // Add tile layer
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(locationMap);

            // Add marker
            locationMarker = L.marker([initialLat, initialLng], {
                draggable: true
            }).addTo(locationMap);

            // Update coordinates when marker is dragged
            locationMarker.on('dragend', function(e) {
                const position = e.target.getLatLng();
                latitudeInput.value = position.lat.toFixed(6);
                longitudeInput.value = position.lng.toFixed(6);

                // Try to get address from coordinates
                fetch(`../reverse_geocode.php?lat=${position.lat}&lng=${position.lng}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.result && data.result.formatted_address) {
                            locationInput.value = data.result.formatted_address;
                        }
                    })
                    .catch(error => {
                        console.error('Error getting location name:', error);
                    });
            });

            // Update marker when clicking on map
            locationMap.on('click', function(e) {
                locationMarker.setLatLng(e.latlng);
                latitudeInput.value = e.latlng.lat.toFixed(6);
                longitudeInput.value = e.latlng.lng.toFixed(6);

                // Try to get address from coordinates
                fetch(`../reverse_geocode.php?lat=${e.latlng.lat}&lng=${e.latlng.lng}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.result && data.result.formatted_address) {
                            locationInput.value = data.result.formatted_address;
                        }
                    })
                    .catch(error => {
                        console.error('Error getting location name:', error);
                    });
            });
        } else {
            locationMap.invalidateSize();
        }
    }

    // Toggle map visibility
    if (toggleMapBtn) {
        toggleMapBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (mapContainer.style.display === 'none') {
                mapContainer.style.display = 'block';
                initMap();
            } else {
                mapContainer.style.display = 'none';
            }
        });
    }

    // Geocode location
    if (geocodeBtn) {
        geocodeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (locationInput.value.trim() !== '') {
                // Show loading indicator
                geocodeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                geocodeBtn.disabled = true;

                // Make a request to our server-side geocoding endpoint
                fetch('../geocode.php?address=' + encodeURIComponent(locationInput.value))
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.result) {
                            latitudeInput.value = data.result.latitude;
                            longitudeInput.value = data.result.longitude;

                            // If map is visible, update it
                            if (mapContainer.style.display !== 'none' && locationMap) {
                                locationMarker.setLatLng([data.result.latitude, data.result.longitude]);
                                locationMap.setView([data.result.latitude, data.result.longitude], 13);
                            }
                        } else {
                            alert('Could not geocode the location. Please try a different address or enter coordinates manually.');
                        }

                        // Reset button
                        geocodeBtn.innerHTML = '<i class="fas fa-search-location"></i>';
                        geocodeBtn.disabled = false;
                    })
                    .catch(error => {
                        console.error('Error geocoding:', error);
                        alert('Error geocoding the location. Please try again or enter coordinates manually.');

                        // Reset button
                        geocodeBtn.innerHTML = '<i class="fas fa-search-location"></i>';
                        geocodeBtn.disabled = false;
                    });
            } else {
                alert('Please enter a location to geocode.');
            }
        });
    }

    // Get current location
    if (getCurrentLocationBtn && navigator.geolocation) {
        getCurrentLocationBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Show loading indicator
            getCurrentLocationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Getting Location...';
            getCurrentLocationBtn.disabled = true;

            navigator.geolocation.getCurrentPosition(function(position) {
                latitudeInput.value = position.coords.latitude;
                longitudeInput.value = position.coords.longitude;

                // If map is visible, update it
                if (mapContainer.style.display !== 'none' && locationMap) {
                    locationMarker.setLatLng([position.coords.latitude, position.coords.longitude]);
                    locationMap.setView([position.coords.latitude, position.coords.longitude], 13);
                }

                // Try to get address from coordinates using our server-side reverse geocoding
                fetch(`../reverse_geocode.php?lat=${position.coords.latitude}&lng=${position.coords.longitude}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.result && data.result.formatted_address) {
                            locationInput.value = data.result.formatted_address;
                        }

                        // Reset button
                        getCurrentLocationBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i> Get Current Location';
                        getCurrentLocationBtn.disabled = false;
                    })
                    .catch(error => {
                        console.error('Error getting location name:', error);

                        // Reset button
                        getCurrentLocationBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i> Get Current Location';
                        getCurrentLocationBtn.disabled = false;
                    });
            }, function(error) {
                console.error('Error getting location:', error);
                alert('Unable to get your location. Please enter it manually.');

                // Reset button
                getCurrentLocationBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i> Get Current Location';
                getCurrentLocationBtn.disabled = false;
            }, {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 0
            });
        });
    }

    // Update map when coordinates are changed manually
    if (latitudeInput && longitudeInput) {
        latitudeInput.addEventListener('change', updateMapFromCoordinates);
        longitudeInput.addEventListener('change', updateMapFromCoordinates);

        function updateMapFromCoordinates() {
            if (latitudeInput.value && longitudeInput.value && locationMap) {
                const lat = parseFloat(latitudeInput.value);
                const lng = parseFloat(longitudeInput.value);

                if (!isNaN(lat) && !isNaN(lng)) {
                    locationMarker.setLatLng([lat, lng]);
                    locationMap.setView([lat, lng], 13);
                }
            }
        }
    }
});
</script>

<?php
// Add mobile-friendly CSS and JS
echo "<style>";
readfile(__DIR__ . '/../assets/css/admin-mobile.css');
readfile(__DIR__ . '/../assets/css/admin-tabs.css');
readfile(__DIR__ . '/../assets/css/sidebar-fix.css'); // Add sidebar fix CSS

// Add custom styles for the geocoding functionality
echo "
.input-with-buttons {
    display: flex;
    align-items: center;
}

.input-with-buttons input {
    flex: 1;
    margin-right: 10px;
}

.input-with-buttons button {
    padding: 8px 12px;
    margin-left: 5px;
}

.map-controls {
    display: flex;
    gap: 10px;
}

.tracking-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 3px 8px;
    border-radius: 4px;
    background-color: rgba(var(--primary-rgb, 92, 43, 226), 0.1);
    color: var(--primary-color, #5c2be2);
    text-decoration: none;
    transition: all 0.3s ease;
}

.tracking-link:hover {
    background-color: rgba(var(--primary-rgb, 92, 43, 226), 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
";
echo "</style>";

echo "<script>";
readfile(__DIR__ . '/../assets/js/admin-mobile.js');
readfile(__DIR__ . '/../assets/js/admin-tabs.js');
echo "</script>";

// Add additional script to ensure sidebar close button works
echo "<script>
// Ensure sidebar close button is properly initialized
document.addEventListener('DOMContentLoaded', function() {
    // Check if sidebar close button exists, if not create it
    const dashboardSidebar = document.querySelector('.dashboard-sidebar');
    if (dashboardSidebar && !dashboardSidebar.querySelector('.sidebar-close')) {
        const closeButton = document.createElement('button');
        closeButton.className = 'sidebar-close';
        closeButton.innerHTML = '<i class=\"fas fa-times\"></i>';
        closeButton.setAttribute('aria-label', 'Close Sidebar');
        closeButton.style.cursor = 'pointer';
        dashboardSidebar.prepend(closeButton);

        // Add event listener to close button
        closeButton.addEventListener('click', function() {
            dashboardSidebar.classList.remove('active');
            const overlay = document.querySelector('.sidebar-overlay');
            if (overlay) overlay.classList.remove('active');
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            if (sidebarToggle) sidebarToggle.classList.remove('active');
            document.body.style.overflow = '';
        });

        console.log('Sidebar close button initialized');
    }
});
</script>";

include_once '../includes/footer.php';
?>

<!-- Script to handle action parameter -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if there's an action parameter in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action');
    const shipmentId = urlParams.get('id');

    if (action && shipmentId) {
        // Handle different actions
        switch(action) {
            case 'add':
                // Open the add shipment modal
                const addShipmentModal = document.getElementById('addShipmentModal');
                if (addShipmentModal) {
                    addShipmentModal.style.display = 'block';
                }
                break;

            case 'view':
                // Open the view shipment modal and load shipment details
                const viewShipmentModal = document.getElementById('viewShipmentModal');
                if (viewShipmentModal) {
                    // Trigger click on the view button for this shipment
                    const viewBtn = document.querySelector(`.view-btn[data-id="${shipmentId}"]`);
                    if (viewBtn) {
                        viewBtn.click();
                    } else {
                        // If button not found, load shipment details directly
                        loadShipmentDetails(shipmentId);
                        viewShipmentModal.style.display = 'block';
                    }
                }
                break;

            case 'edit':
                // Open the edit shipment modal and load shipment data
                const editShipmentModal = document.getElementById('editShipmentModal');
                if (editShipmentModal) {
                    // Trigger click on the edit button for this shipment
                    const editBtn = document.querySelector(`.edit-btn[data-id="${shipmentId}"]`);
                    if (editBtn) {
                        editBtn.click();
                    } else {
                        // If button not found, load shipment data via AJAX
                        fetch(`get-shipment.php?id=${shipmentId}`)
                            .then(response => response.json())
                            .then(data => {
                                if (data.success && data.shipment) {
                                    // Populate the edit form with shipment data
                                    populateEditForm(data.shipment);
                                    editShipmentModal.style.display = 'block';
                                } else {
                                    alert('Error loading shipment data');
                                }
                            })
                            .catch(error => {
                                console.error('Error loading shipment data:', error);
                                alert('Error loading shipment data');
                            });
                    }
                }
                break;

            case 'add_update':
                // Open the add update modal and set the shipment ID
                const addUpdateModal = document.getElementById('addUpdateModal');
                if (addUpdateModal) {
                    // Set the shipment ID in the form
                    const shipmentIdInput = document.getElementById('update_shipment_id');
                    if (shipmentIdInput) {
                        shipmentIdInput.value = shipmentId;
                    }

                    // Trigger click on the update button for this shipment
                    const updateBtn = document.querySelector(`.update-btn[data-id="${shipmentId}"]`);
                    if (updateBtn) {
                        updateBtn.click();
                    } else {
                        // If button not found, just show the modal
                        addUpdateModal.style.display = 'block';
                    }
                }
                break;
        }
    } else if (action === 'add') {
        // Handle just the add action without an ID
        const addShipmentModal = document.getElementById('addShipmentModal');
        if (addShipmentModal) {
            addShipmentModal.style.display = 'block';
        }
    }
});

// Helper function to populate the edit form with shipment data
function populateEditForm(shipment) {
    // Set basic info
    document.getElementById('edit_shipment_id').value = shipment.id;
    document.getElementById('edit_tracking_number').value = shipment.tracking_number;
    document.getElementById('edit_customer_name').value = shipment.customer_name;
    document.getElementById('edit_origin').value = shipment.origin;
    document.getElementById('edit_destination').value = shipment.destination;
    document.getElementById('edit_status').value = shipment.status;

    // Set estimated delivery date if available
    if (shipment.estimated_delivery) {
        document.getElementById('edit_estimated_delivery').value = shipment.estimated_delivery.split(' ')[0]; // Get just the date part
    }

    // Set shipment info if available
    if (shipment.shipment_name) document.getElementById('edit_shipment_name').value = shipment.shipment_name;
    if (shipment.shipment_description) document.getElementById('edit_shipment_description').value = shipment.shipment_description;

    // Set shopper info if available
    if (shipment.shopper_name) document.getElementById('edit_shopper_name').value = shipment.shopper_name;
    if (shipment.shopper_email) document.getElementById('edit_shopper_email').value = shipment.shopper_email;
    if (shipment.shopper_phone) document.getElementById('edit_shopper_phone').value = shipment.shopper_phone;
    if (shipment.shopper_address) document.getElementById('edit_shopper_address').value = shipment.shopper_address;

    // Set receiver info if available
    if (shipment.receiver_name) document.getElementById('edit_receiver_name').value = shipment.receiver_name;
    if (shipment.receiver_email) document.getElementById('edit_receiver_email').value = shipment.receiver_email;
    if (shipment.receiver_phone) document.getElementById('edit_receiver_phone').value = shipment.receiver_phone;
    if (shipment.receiver_address) document.getElementById('edit_receiver_address').value = shipment.receiver_address;

    // Set package info if available
    if (shipment.package_name) document.getElementById('edit_package_name').value = shipment.package_name;
    if (shipment.package_description) document.getElementById('edit_package_description').value = shipment.package_description;
    if (shipment.package_weight) document.getElementById('edit_package_weight').value = shipment.package_weight;
    if (shipment.package_dimensions) document.getElementById('edit_package_dimensions').value = shipment.package_dimensions;
    if (shipment.shipping_service) document.getElementById('edit_shipping_service').value = shipment.shipping_service;
    if (shipment.shipping_cost) document.getElementById('edit_shipping_cost').value = shipment.shipping_cost;

    // Show current package picture if available
    const currentPackagePicture = document.getElementById('current_package_picture');
    if (currentPackagePicture && shipment.package_picture) {
        currentPackagePicture.innerHTML = `<img src="../uploads/packages/${shipment.package_picture}" alt="Current Package Picture" style="max-width: 100%; max-height: 200px;">`;
    }
}
</script>
