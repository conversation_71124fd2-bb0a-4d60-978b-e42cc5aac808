/* Mobile Optimization Styles
 * This file contains comprehensive mobile-first optimizations
 * to improve the mobile experience across the entire site
 */

/* ===== Base Mobile Optimizations ===== */

/* Improve tap targets for mobile */
@media (max-width: 768px) {
    /* Make all interactive elements have at least 44px touch target */
    button,
    .btn,
    a.btn,
    .action-btn,
    input[type="submit"],
    input[type="button"],
    .nav-link,
    .mobile-menu-toggle,
    .theme-toggle button,
    .sidebar-toggle {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Increase spacing between interactive elements */
    nav ul li,
    .action-btn,
    .form-group,
    .btn-group .btn {
        margin-bottom: 12px;
    }

    /* Improve form elements for touch */
    input,
    select,
    textarea {
        font-size: 16px; /* Prevents iOS zoom on focus */
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 16px;
    }

    /* Checkbox and radio improvements */
    input[type="checkbox"],
    input[type="radio"] {
        width: 22px;
        height: 22px;
        margin-right: 10px;
    }

    label {
        display: block;
        margin-bottom: 12px;
    }

    /* Specific fix for tracking form labels */
    .tracking-form label {
        margin-bottom: 35px;
        display: block;
        position: relative;
        font-size: 0.85rem;
    }

    /* Add space between label and input */
    .tracking-form input {
        padding-top: 15px;
    }
}

/* ===== Mobile Typography ===== */

@media (max-width: 768px) {
    /* Fluid typography for better readability */
    body {
        font-size: 16px;
        line-height: 1.6;
    }

    /* Adjust heading sizes for mobile */
    h1, .h1 {
        font-size: clamp(1.8rem, 8vw, 2.5rem);
        line-height: 1.2;
        margin-bottom: 16px;
    }

    h2, .h2 {
        font-size: clamp(1.5rem, 6vw, 2rem);
        line-height: 1.3;
        margin-bottom: 14px;
    }

    h3, .h3 {
        font-size: clamp(1.2rem, 5vw, 1.5rem);
        line-height: 1.3;
        margin-bottom: 12px;
    }

    /* Improve paragraph readability */
    p {
        font-size: 1rem;
        margin-bottom: 16px;
    }
}

/* ===== Mobile Layout Improvements ===== */

@media (max-width: 768px) {
    /* Add more breathing room */
    .container {
        padding-left: 16px;
        padding-right: 16px;
    }

    /* Improve section spacing */
    section {
        padding: 40px 0;
    }

    /* Better card layouts */
    .card,
    .glass-card,
    .service-card,
    .industry-card,
    .stat-card,
    .summary-card {
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 12px;
    }

    /* Ensure all grids stack properly */
    .grid,
    .stats-grid,
    .services-grid,
    .industries-grid,
    .dashboard-grid,
    .metrics-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* Improve spacing in flex containers */
    .flex-container,
    .flex-row,
    .admin-actions,
    .form-row {
        flex-direction: column;
        gap: 16px;
    }

    /* Full-width buttons on mobile */
    .btn,
    .action-btn,
    .primary-btn,
    .secondary-btn,
    .outline-btn {
        width: 100%;
        text-align: center;
        justify-content: center;
    }

    /* Button groups should stack */
    .btn-group,
    .action-group,
    .cta-buttons {
        flex-direction: column;
        width: 100%;
        gap: 12px;
    }
}

/* ===== Mobile Navigation Enhancements ===== */

@media (max-width: 768px) {
    /* Improve mobile menu toggle */
    .mobile-menu-toggle {
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border-radius: 50%;
        border: var(--glass-border);
        box-shadow: var(--glass-shadow);
        z-index: 1001;
    }

    /* Enhanced mobile navigation */
    nav#main-nav {
        padding-top: 80px;
    }

    nav#main-nav ul {
        gap: 8px;
    }

    nav#main-nav ul li a {
        padding: 12px 16px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        width: 100%;
        font-weight: 500;
    }

    nav#main-nav ul li a i {
        margin-right: 12px;
        width: 20px;
        text-align: center;
    }

    /* Active state for mobile nav */
    nav#main-nav ul li a.active {
        background-color: rgba(var(--primary-rgb), 0.1);
        color: var(--primary-color);
    }
}

/* ===== Mobile Table Optimizations ===== */

@media (max-width: 768px) {
    /* Card-based tables for mobile */
    .mobile-card-table {
        display: block;
        width: 100%;
        border: none;
    }

    .mobile-card-table thead {
        display: none; /* Hide table headers */
    }

    .mobile-card-table tbody {
        display: block;
        width: 100%;
    }

    .mobile-card-table tr {
        display: block;
        margin-bottom: 16px;
        border-radius: 8px;
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: var(--glass-border);
        box-shadow: var(--glass-shadow);
        padding: 16px;
    }

    .mobile-card-table td {
        display: flex;
        padding: 8px 0;
        border: none;
        text-align: left;
        align-items: center;
    }

    .mobile-card-table td::before {
        content: attr(data-label);
        font-weight: 600;
        width: 40%;
        margin-right: 16px;
    }

    /* Actions in mobile tables */
    .mobile-card-table td.actions {
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 8px;
    }

    .mobile-card-table td.actions .action-btn {
        margin: 0;
    }
}

/* ===== Mobile Dashboard Improvements ===== */

@media (max-width: 768px) {
    /* Dashboard layout */
    .admin-hero {
        text-align: center;
    }

    /* Stats cards in a more compact layout */
    .stat-card {
        display: grid;
        grid-template-columns: auto 1fr;
        align-items: center;
        gap: 16px;
        padding: 16px;
    }

    .stat-icon {
        grid-row: span 2;
        margin: 0;
    }

    .stat-info {
        text-align: left;
    }

    /* Improve chart visibility */
    .chart-wrapper {
        margin-bottom: 30px;
        height: auto;
        min-height: 250px;
    }

    /* Better sidebar toggle */
    .sidebar-toggle {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        z-index: 999;
    }
}

/* ===== Mobile Form Improvements ===== */

@media (max-width: 768px) {
    /* Form layout */
    .form-container {
        padding: 20px;
    }

    /* Floating labels for better UX */
    .form-floating {
        position: relative;
        margin-bottom: 20px;
    }

    .form-floating input,
    .form-floating select,
    .form-floating textarea {
        height: 56px;
        padding: 20px 16px 8px;
    }

    .form-floating textarea {
        height: auto;
        min-height: 100px;
    }

    .form-floating label {
        position: absolute;
        top: 8px;
        left: 16px;
        font-size: 12px;
        color: var(--text-secondary);
        pointer-events: none;
    }

    /* Better select dropdowns */
    select {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 16px center;
        padding-right: 40px;
        appearance: none;
    }
}

/* ===== Mobile Hero Section Improvements ===== */

@media (max-width: 768px) {
    /* Hero section */
    .hero {
        padding: 100px 0 40px;
        text-align: center;
    }

    .hero-content {
        max-width: 100%;
    }

    /* Tracking box */
    .tracking-box {
        padding: 20px;
        margin-top: 20px;
    }

    .tracking-form input {
        padding: 14px;
        font-size: 16px;
    }

    .tracking-form button {
        padding: 14px;
    }
}

/* ===== Mobile Footer Improvements ===== */

@media (max-width: 768px) {
    /* Footer layout */
    .footer-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .footer-column {
        text-align: center;
    }

    .footer-links {
        justify-content: center;
    }

    .social-links {
        justify-content: center;
    }
}

/* ===== Mobile-specific Animations ===== */

@media (max-width: 768px) {
    /* Reduce or eliminate animations that might cause performance issues */
    .service-card:hover,
    .industry-card:hover,
    .btn:hover {
        transform: none;
    }

    /* Use simpler hover effects */
    .btn:active,
    .action-btn:active,
    a:active {
        opacity: 0.7;
    }
}

/* ===== Dark Theme Mobile Adjustments ===== */

@media (max-width: 768px) {
    .dark-theme .mobile-menu-toggle {
        background: rgba(30, 30, 40, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dark-theme .sidebar-toggle {
        background: var(--primary-dark);
    }
}

/* ===== Mobile Tracking Page Improvements ===== */

@media (max-width: 768px) {
    /* Tracking info layout */
    .tracking-info {
        flex-direction: column;
    }

    .tracking-details {
        width: 100%;
    }

    .tracking-map-container {
        width: 100%;
        margin-top: 20px;
    }

    /* Map section grid improvements */
    .map-section-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 30px;
    }

    /* Adjust map height for mobile */
    #tracking-map {
        height: 300px !important;
        position: relative;
        z-index: 5;
    }

    /* Ensure current location info doesn't overlap */
    .current-location-info {
        position: relative;
        z-index: 10;
        margin-top: 20px;
    }

    /* Shipment progress */
    .shipment-progress {
        padding: 16px;
        margin-top: 20px;
    }

    .progress-step {
        padding-left: 30px;
    }

    .progress-step::before {
        left: 0;
    }

    /* Share button - position relative instead of fixed to prevent overlapping */
    .share-tracking {
        position: relative;
        margin-top: 20px;
        margin-bottom: 10px;
        z-index: 20;
        width: 100%;
        display: flex;
        justify-content: center;
    }

    .share-btn {
        width: auto;
        padding: 10px 20px;
        border-radius: 30px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    }

    /* Improve shipment details grid on mobile */
    .shipment-details-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* Adjust timeline for better mobile display */
    .timeline {
        margin-left: 10px;
    }
}

/* ===== Mobile Login/Register Improvements ===== */

@media (max-width: 768px) {
    .auth-container {
        width: 100%;
        padding: 20px;
    }

    .auth-form {
        padding: 20px;
    }

    .auth-options {
        flex-direction: column;
        gap: 10px;
    }
}

/* ===== Mobile Notification Improvements ===== */

@media (max-width: 768px) {
    .notification {
        width: 90%;
        max-width: none;
        left: 5%;
        right: 5%;
        padding: 15px;
    }

    .notification-content {
        font-size: 14px;
    }
}

/* ===== Pull to Refresh ===== */

.refresh-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: transform 0.3s ease;
    transform: translateY(-100%);
}

.refresh-indicator i {
    margin-right: 10px;
}

.refresh-indicator.ready {
    background-color: var(--secondary-color);
}

.refresh-indicator i.rotating {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
