<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Get user information
$userId = $_SESSION['user_id'];
$userInfo = getUserById($userId);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_support'])) {
    $subject = sanitize($_POST['subject']);
    $message = sanitize($_POST['message']);
    $priority = sanitize($_POST['priority']);

    // Validate input
    if (empty($subject) || empty($message)) {
        setErrorNotification('Subject and message are required fields', 10000);
    } else {
        // Prepare form data for email
        $formData = [
            'subject' => $subject,
            'message' => $message,
            'priority' => $priority
        ];

        // Send email notification to admin
        $emailSent = sendSupportRequestEmail($formData, $userId, $_SESSION['username']);

        if ($emailSent) {
            // Set success notification
            setSuccessNotification('Your support request has been submitted. We will get back to you shortly.', 10000);
        } else {
            // Email failed to send
            setErrorNotification('There was a problem submitting your request. Please try again later or contact us directly by phone.', 10000);
            // Log the error
            debugLog('Failed to send support request email', $formData);
        }

        // Redirect to prevent form resubmission
        header('Location: support.php');
        exit;
    }
}

// Set page title
$pageTitle = 'Support';

// Include header
include_once 'includes/header.php';
?>

<div class="container">
    <div class="page-header">
        <h1>Customer Support</h1>
        <p>Need help? Submit a support request and we'll get back to you as soon as possible.</p>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="support-info glass-card">
                <h3>Contact Information</h3>
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <div>
                        <h4>Phone Support</h4>
                        <p>+****************</p>
                        <p class="text-muted">Monday - Friday: 9am - 6pm</p>
                    </div>
                </div>

                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <div>
                        <h4>Email Support</h4>
                        <p><EMAIL></p>
                        <p class="text-muted">24/7 support</p>
                    </div>
                </div>

                <div class="contact-item">
                    <i class="fas fa-comments"></i>
                    <div>
                        <h4>Live Chat</h4>
                        <p>Available on our website</p>
                        <p class="text-muted">Monday - Friday: 9am - 6pm</p>
                    </div>
                </div>
            </div>

            <div class="faq-section glass-card">
                <h3>Frequently Asked Questions</h3>
                <div class="faq-item">
                    <div class="faq-question">
                        <h4>How do I track my shipment?</h4>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>You can track your shipment by entering your tracking number in the tracking form on our homepage or by visiting the tracking page directly.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h4>What if my shipment is delayed?</h4>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>If your shipment is delayed, you will receive a notification with the reason for the delay and the updated estimated delivery date. You can also check the status of your shipment on the tracking page.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h4>How can I change the delivery address?</h4>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>To change the delivery address, please contact our customer support team as soon as possible. Changes may not be possible once the shipment is in transit.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h4>What shipping services do you offer?</h4>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>We offer various shipping services including Standard, Express, Priority, and Economy shipping. Each service has different delivery timeframes and pricing.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="support-form glass-card">
                <h3>Submit a Support Request</h3>
                <form action="support.php" method="POST">
                    <div class="form-group">
                        <label for="name">Your Name</label>
                        <input type="text" id="name" name="name" value="<?php echo htmlspecialchars(($userInfo['first_name'] ?? '') . ' ' . ($userInfo['last_name'] ?? '')); ?>" readonly>
                    </div>

                    <div class="form-group">
                        <label for="email">Your Email</label>
                        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($userInfo['email'] ?? ''); ?>" readonly>
                    </div>

                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" id="subject" name="subject" required>
                    </div>

                    <div class="form-group">
                        <label for="priority">Priority</label>
                        <select id="priority" name="priority">
                            <option value="low">Low</option>
                            <option value="medium" selected>Medium</option>
                            <option value="high">High</option>
                            <option value="urgent">Urgent</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="message">Message</label>
                        <textarea id="message" name="message" rows="6" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="attachment">Attachment (optional)</label>
                        <input type="file" id="attachment" name="attachment">
                        <small>Max file size: 5MB. Allowed file types: jpg, jpeg, png, pdf, doc, docx</small>
                    </div>

                    <div class="form-actions">
                        <button type="submit" name="submit_support" class="btn primary-btn">Submit Request</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // FAQ accordion functionality
        const faqQuestions = document.querySelectorAll('.faq-question');

        faqQuestions.forEach(question => {
            question.addEventListener('click', function() {
                const answer = this.nextElementSibling;
                const icon = this.querySelector('i');

                // Toggle answer visibility
                if (answer.style.maxHeight) {
                    answer.style.maxHeight = null;
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                } else {
                    answer.style.maxHeight = answer.scrollHeight + 'px';
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                }
            });
        });
    });
</script>

<style>
    .page-header {
        margin-bottom: 30px;
        padding-top: 30px;
    }

    .page-header h1 {
        color: var(--primary-color);
        margin-bottom: 10px;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -15px;
    }

    .col-md-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
        padding: 0 15px;
    }

    .col-md-8 {
        flex: 0 0 66.666667%;
        max-width: 66.666667%;
        padding: 0 15px;
    }

    .glass-card {
        background-color: var(--glass-bg);
        border-radius: 16px;
        padding: 25px;
        margin-bottom: 30px;
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: var(--glass-border);
        box-shadow: var(--glass-shadow);
    }

    .support-info h3,
    .faq-section h3,
    .support-form h3 {
        color: var(--primary-color);
        margin-bottom: 20px;
    }

    .contact-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;
    }

    .contact-item i {
        font-size: 1.5rem;
        color: var(--primary-color);
        margin-right: 15px;
        margin-top: 5px;
    }

    .contact-item h4 {
        margin: 0 0 5px;
        font-size: 1.1rem;
    }

    .contact-item p {
        margin: 0;
    }

    .text-muted {
        color: var(--text-secondary);
        font-size: 0.9rem;
    }

    .faq-item {
        margin-bottom: 15px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .faq-question {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        cursor: pointer;
    }

    .faq-question h4 {
        margin: 0;
        font-size: 1.1rem;
    }

    .faq-question i {
        color: var(--primary-color);
        transition: transform 0.3s ease;
    }

    .faq-answer {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }

    .faq-answer p {
        padding: 10px 0;
        margin: 0;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        background-color: rgba(255, 255, 255, 0.8);
    }

    .form-group input[readonly] {
        background-color: rgba(0, 0, 0, 0.05);
        cursor: not-allowed;
    }

    .form-group textarea {
        resize: vertical;
    }

    .form-group input[type="file"] {
        padding: 10px;
    }

    .form-group small {
        display: block;
        margin-top: 5px;
        color: var(--text-secondary);
        font-size: 0.8rem;
    }

    .form-actions {
        margin-top: 30px;
    }

    /* Dark theme adjustments */
    .dark-theme .glass-card {
        background-color: rgba(30, 30, 40, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dark-theme .faq-item {
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .dark-theme .form-group input,
    .dark-theme .form-group select,
    .dark-theme .form-group textarea {
        background-color: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.1);
        color: white;
    }

    .dark-theme .form-group input[readonly] {
        background-color: rgba(0, 0, 0, 0.3);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .row {
            flex-direction: column;
        }

        .col-md-4,
        .col-md-8 {
            max-width: 100%;
            flex: 0 0 100%;
        }
    }
</style>

<?php
// Include footer
include_once 'includes/footer.php';
?>
