/* Global Styles */
:root {
    /* Theme colors */
    --primary-color: #5c2be2; /* Main purple */
    --primary-rgb: 92, 43, 226; /* RGB values for opacity control */
    --primary-dark: #4f25c2; /* Darker purple for hover */
    --primary-light: #7e56e8; /* Lighter purple for accents */

    --secondary-color:rgb(0, 212, 95); /* Teal - complementary color */
    --secondary-color-rgb: 0, 212, 95; /* RGB values for opacity control */
    --secondary-dark:rgb(0, 179, 69); /* Darker teal for hover */
    --secondary-light:rgb(78, 236, 86); /* Lighter teal for accents */

    /* Light theme (default) */
    --bg-color: #ffffff;
    --bg-secondary: #f4f7fb;
    --text-color: #333333;
    --text-secondary: #666666;
    --border-color: #e1e1e1;
    --box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);

    /* Glassmorphism */
    --glass-bg: rgba(255, 255, 255, 0.15);
    --glass-border: 1px solid rgba(255, 255, 255, 0.3);
    --glass-shadow: 0 8px 32px 0 rgba(92, 43, 226, 0.2);
    --glass-blur: blur(4px);
}

/* Dark theme class to be toggled with JavaScript */
.dark-theme {
    --bg-color: #121212;
    --bg-secondary: #1e1e1e;
    --text-color: #f5f5f5;
    --text-secondary: #cccccc;
    --border-color: #2c2c2c;
    --box-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);

    /* Glassmorphism for dark theme */
    --glass-bg: rgba(30, 30, 30, 0.15);
    --glass-border: 1px solid rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
}

ul {
    list-style: none;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    /* Glassmorphism effect */
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.primary-btn:hover {
    background-color: var(--secondary-color);
    box-shadow: 0 8px 25px rgba(92, 43, 226, 0.4);
    transform: translateY(-2px);
}

.secondary-btn {
    background-color: rgba(92, 43, 226, 0.1);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.secondary-btn:hover {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 8px 25px rgba(92, 43, 226, 0.4);
    transform: translateY(-2px);
}

.teal-btn {
    background-color: var(--secondary-color);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.teal-btn:hover {
    background-color: var(--secondary-dark);
    box-shadow: 0 8px 25px rgba(0, 212, 177, 0.4);
    transform: translateY(-2px);
}

.section-title {
    font-size: 2.2rem;
    margin-bottom: 15px;
    color: var(--dark-color);
    text-align: center;
}

.section-subtitle {
    font-size: 1.1rem;
    margin-bottom: 40px;
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Header Styles */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    position: fixed; /* Change to fixed to make it sticky while floating */
    top: 20px; /* Add some space from the top */
    left: 50%; /* Center horizontally */
    transform: translateX(-50%); /* Center horizontally */
    z-index: 100;
    transition: all 0.3s ease;
    width: 90%; /* Slightly narrower than 100% */
    max-width: 1200px; /* Maximum width */
    border-radius: 15px; /* Rounded corners */
    margin: 0 auto; /* Center the header */
}

.dark-theme header {
    background: rgba(30, 30, 30, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    z-index: 101;
}

.logo .logo-content { /* Style the new logo container */
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2rem; /* Reduced logo text size */
    font-weight: 600;
    color: var(--primary-color);
}

.logo .logo-icon {
    font-size: 1.8rem; /* Adjust icon size */
    color: var(--secondary-color);
}

nav {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
}

nav ul {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
}

nav ul li {
    margin-left: 20px; /* Adjust spacing if needed */
}

nav ul li:first-child {
    margin-left: 0; /* Remove margin from the first item */
}

nav ul li a {
    color: var(--dark-color);
    font-weight: 500;
    transition: color 0.3s ease;
}

nav ul li a:hover {
    color: var(--secondary-color);
    transform: translateY(-2px);
}

.login-btn a {
    background-color: var(--primary-color);
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    /* Glassmorphism effect */
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.login-btn a:hover {
    box-shadow: 0 8px 25px rgba(92, 43, 226, 0.4);
    transform: translateY(-2px);
}

/* Theme Toggle Button */
.theme-toggle {
    margin-left: 15px;
}

.theme-toggle button {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.theme-toggle button:hover {
    background-color: var(--glass-bg);
    transform: translateY(-2px);
}

.dark-theme .theme-toggle button {
    color: var(--secondary-color);
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(92, 43, 226, 0.8), rgba(79, 37, 194, 0.8)), url('https://images.unsplash.com/photo-1494412651409-8963ce7935a7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80') no-repeat center center/cover;
    color: white;
    padding: 100px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    z-index: 0;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 20px;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* Tracking Section */
.tracking {
    padding: 60px 0;
    background-color: var(--bg-secondary);
    transition: background-color 0.3s ease;
}

.tracking-box {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    max-width: 800px;
    margin: 0 auto;
    /* Glassmorphism effect */
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    transition: background-color 0.3s ease, box-shadow 0.3s ease, border 0.3s ease;
}

.tracking-box h2 {
    text-align: center;
    margin-bottom: 25px;
    color: var(--dark-color);
}

.tracking-form {
    margin-bottom: 30px;
}


.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    font-size: 1rem;
    color: var(--text-color);
    /* Glassmorphism effect */
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: 0 4px 12px rgba(31, 38, 135, 0.1);
    transition: all 0.3s ease, color 0.3s ease, background 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border: 1px solid var(--primary-color);
    box-shadow: 0 8px 20px rgba(92, 43, 226, 0.2);
}

.route-search h3 {
    margin-bottom: 15px;
    text-align: center;
    color: var(--dark-color);
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row .form-group {
    flex: 1;
}

/* Services Section */
.services {
    padding: 80px 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.service-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease, background-color 0.3s ease, border 0.3s ease;
    /* Glassmorphism effect */
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    color: var(--text-color);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(92, 43, 226, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.service-icon {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 20px;
    transition: color 0.3s ease;
}

.service-card h3 {
    margin-bottom: 15px;
    color: var(--dark-color);
}

.service-card p {
    margin-bottom: 20px;
}

.learn-more {
    font-weight: 600;
    color: var(--secondary-color);
    transition: color 0.3s ease;
}

.learn-more:hover {
    color: var(--primary-color);
}

/* Industries Section */
.industries {
    padding: 80px 0;
    background-color: var(--bg-secondary);
    transition: background-color 0.3s ease;
}

.industries-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 20px;
}

.industry-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease, background-color 0.3s ease, border 0.3s ease;
    /* Glassmorphism effect */
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    color: var(--text-color);
}

.industry-card:hover {
    transform: translateY(-5px);
    background-color: var(--primary-color);
    color: rgb(101, 148, 209);
    box-shadow: 0 15px 30px rgba(141, 106, 236, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.industry-card:hover .industry-icon {
    color: white;
}

.industry-icon {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 15px;
    transition: color 0.3s ease;
}

.industry-card h3 {
    font-size: 1.1rem;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background: linear-gradient(rgba(92, 43, 226, 0.9), rgba(79, 37, 194, 0.9)), url('https://images.unsplash.com/photo-1566576721346-d4a3b4eaeb55?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80') no-repeat center center/cover;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    z-index: 0;
}

.cta-content {
    position: relative;
    z-index: 1;
    max-width: 700px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.cta-content p {
    font-size: 1.1rem;
    margin-bottom: 30px;
}

/* Footer */
footer {
    background-color: var(--bg-secondary);
    color: var(--text-color);
    padding: 60px 0 20px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.dark-theme footer {
    background-color: #0a0a0a;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.footer-col h3, .footer-col h4 {
    margin-bottom: 20px;
    color: var(--text-color);
}

.footer-col p {
    margin-bottom: 20px;
    color: var(--text-secondary);
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    color: var(--text-color);
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.social-icons a:hover {
    color: var(--secondary-color);
}

.footer-col ul li {
    margin-bottom: 10px;
}

.footer-col ul li a {
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.footer-col ul li a:hover {
    color: var(--secondary-color);
}

.contact-info li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    color: var(--text-secondary);
}

.footer-bottom {
    border-top: 1px solid var(--border-color);
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.footer-bottom p {
    color: var(--text-secondary);
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--secondary-color);
}

/* Mobile Menu Styles */
.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
    transition: color 0.3s ease;
    z-index: 101;
}

.mobile-menu-toggle:hover {
    color: var(--primary-color);
}

/* Overlay for mobile menu */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

body.menu-active::after {
    opacity: 1;
    visibility: visible;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .mobile-menu-toggle {
        display: block;
        z-index: 101;
    }

    nav {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        max-width: 300px;
        height: 100vh;
        background-color: var(--glass-bg);
        box-shadow: var(--box-shadow);
        transition: left 0.3s ease;
        z-index: 100;
        padding: 80px 20px 20px;
        overflow-y: auto;
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border-right: var(--glass-border);
    }

    .dark-theme nav {
        background-color: rgba(30, 30, 30, 0.8);
        border-right: 1px solid rgba(255, 255, 255, 0.1);
    }

    nav.active {
        left: 0;
    }

    nav ul {
        flex-direction: column;
        align-items: flex-start;
    }

    nav ul li {
        margin: 10px 0;
        width: 100%;
    }

    nav ul li a {
        padding: 10px 0;
        display: block;
        width: 100%;
    }

    .login-btn {
        margin-top: 15px;
    }

    .login-btn a {
        width: 100%;
        text-align: center;
    }

    .theme-toggle {
        margin-top: 15px;
        margin-left: 0;
    }

    .theme-toggle button {
        width: 100%;
        justify-content: flex-start;
    }
}

@media (max-width: 768px) {
    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .cta-buttons .btn {
        width: 100%;
    }

    .form-row {
        flex-direction: column;
    }

    .tracking-box {
        padding: 20px;
    }

    .services-grid, .industries-grid {
        grid-template-columns: 1fr;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .footer-links {
        margin-top: 15px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .section-title {
        font-size: 1.8rem;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 1.8rem;
    }

    .logo-content span {
        font-size: 1.2rem;
    }

    .cta-content h2 {
        font-size: 1.8rem;
    }

    .footer-grid {
        grid-template-columns: 1fr;
    }
}
