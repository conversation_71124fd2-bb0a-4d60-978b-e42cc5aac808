/**
 * Logo Generator for Global TransLogix
 * This script creates different logo variations that can be selected
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get the logo container
    const logoContainer = document.querySelector('.logo');
    if (!logoContainer) return;
    
    // Get site name from settings or use default
    const siteName = document.documentElement.getAttribute('data-site-name') || 'Global TransLogix';
    
    // Logo variations
    const logoVariations = [
        {
            id: 'pin-track',
            icon: '<i class="fas fa-map-marker-alt logo-icon-inner"></i>',
            name: siteName,
            tagline: 'Worldwide Tracking Solutions'
        },
        {
            id: 'globe-route',
            icon: '<i class="fas fa-globe-americas logo-icon-inner"></i>',
            name: siteName,
            tagline: 'Global Logistics Network'
        },
        {
            id: 'box-track',
            icon: '<i class="fas fa-box logo-icon-inner"></i>',
            name: siteName,
            tagline: 'Shipment Tracking Experts'
        },
        {
            id: 'route-path',
            icon: '<i class="fas fa-route logo-icon-inner"></i>',
            name: siteName,
            tagline: 'Track Every Step'
        },
        {
            id: 'truck-delivery',
            icon: '<i class="fas fa-truck logo-icon-inner"></i>',
            name: siteName,
            tagline: 'Reliable Delivery Tracking'
        }
    ];
    
    // Get the selected logo variation from localStorage or use default
    const selectedVariation = localStorage.getItem('selectedLogo') || 'pin-track';
    
    // Find the selected variation
    const logoData = logoVariations.find(v => v.id === selectedVariation) || logoVariations[0];
    
    // Create the logo HTML
    const logoHTML = `
        <a href="${window.location.origin}" class="site-logo">
            <div class="logo-icon">
                <div class="logo-icon-bg"></div>
                ${logoData.icon}
            </div>
            <div class="logo-text">
                <span class="logo-text-main">${logoData.name}</span>
                <span class="logo-text-tagline">${logoData.tagline}</span>
            </div>
        </a>
    `;
    
    // Set the logo HTML
    logoContainer.innerHTML = logoHTML;
    
    // Add logo preview functionality for admin
    if (document.body.classList.contains('admin-page')) {
        createLogoPreview();
    }
    
    /**
     * Create logo preview for admin pages
     */
    function createLogoPreview() {
        // Create logo preview container
        const previewContainer = document.createElement('div');
        previewContainer.className = 'logo-preview-container';
        previewContainer.innerHTML = `
            <h3>Logo Preview</h3>
            <div class="logo-variations">
                ${logoVariations.map(variation => `
                    <div class="logo-variation ${variation.id === selectedVariation ? 'selected' : ''}" data-id="${variation.id}">
                        <div class="logo-icon">
                            <div class="logo-icon-bg"></div>
                            ${variation.icon}
                        </div>
                        <div class="logo-text">
                            <span class="logo-text-main">${variation.name}</span>
                            <span class="logo-text-tagline">${variation.tagline}</span>
                        </div>
                    </div>
                `).join('')}
            </div>
            <button id="save-logo" class="btn primary-btn">Save Logo Selection</button>
        `;
        
        // Add to settings section if it exists
        const settingsSection = document.querySelector('.settings-section');
        if (settingsSection) {
            settingsSection.appendChild(previewContainer);
            
            // Add event listeners
            const variations = document.querySelectorAll('.logo-variation');
            variations.forEach(variation => {
                variation.addEventListener('click', function() {
                    // Remove selected class from all variations
                    variations.forEach(v => v.classList.remove('selected'));
                    // Add selected class to clicked variation
                    this.classList.add('selected');
                });
            });
            
            // Save button
            const saveButton = document.getElementById('save-logo');
            if (saveButton) {
                saveButton.addEventListener('click', function() {
                    const selected = document.querySelector('.logo-variation.selected');
                    if (selected) {
                        const selectedId = selected.getAttribute('data-id');
                        localStorage.setItem('selectedLogo', selectedId);
                        
                        // Update the actual logo
                        const selectedLogo = logoVariations.find(v => v.id === selectedId);
                        if (selectedLogo) {
                            logoContainer.querySelector('.logo-icon-inner').outerHTML = selectedLogo.icon;
                            logoContainer.querySelector('.logo-text-main').textContent = selectedLogo.name;
                            logoContainer.querySelector('.logo-text-tagline').textContent = selectedLogo.tagline;
                        }
                        
                        // Show success message
                        if (typeof showSuccess === 'function') {
                            showSuccess('Logo updated successfully');
                        } else {
                            alert('Logo updated successfully');
                        }
                    }
                });
            }
        }
    }
});
