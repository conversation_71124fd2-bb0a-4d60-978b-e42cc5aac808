/* Enhanced Design Elements - Glassmorphism, Gradients, Textures */

/* Additional CSS Variables */
:root {
    /* Gradient backgrounds */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    --gradient-light: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    --gradient-dark: linear-gradient(135deg, #343a40 0%, #212529 100%);
    --gradient-purple-teal: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);

    /* Enhanced glassmorphism */
    --glass-bg-strong: rgba(255, 255, 255, 0.25);
    --glass-bg-medium: rgba(255, 255, 255, 0.15);
    --glass-bg-light: rgba(255, 255, 255, 0.08);
    --glass-blur-strong: blur(15px);
    --glass-blur-medium: blur(10px);
    --glass-blur-light: blur(5px);

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.2);
    --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.05);

    /* Spacing */
    --section-spacing: 80px;
    --card-spacing: 30px;
}

.dark-theme {
    /* Dark theme gradient backgrounds */
    --gradient-light: linear-gradient(135deg, #2c3e50 0%, #1a1a2e 100%);
    --gradient-dark: linear-gradient(135deg, #0f0c29 0%, #302b63 100%);

    /* Enhanced glassmorphism for dark theme */
    --glass-bg-strong: rgba(30, 30, 30, 0.25);
    --glass-bg-medium: rgba(30, 30, 30, 0.15);
    --glass-bg-light: rgba(30, 30, 30, 0.08);
}

/* ===== SECTION ENHANCEMENTS ===== */

/* Add texture overlay to sections */
.textured-section {
    position: relative;
    overflow: hidden;
}

.textured-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/textures/subtle-dots.png');
    opacity: 0.05;
    pointer-events: none;
    z-index: 1;
}

.textured-section > * {
    position: relative;
    z-index: 2;
}

/* Gradient background sections */
.gradient-primary-section {
    background: var(--gradient-primary);
    color: white;
}

.gradient-secondary-section {
    background: var(--gradient-secondary);
    color: white;
}

.gradient-light-section {
    background: var(--gradient-light);
}

.gradient-dark-section {
    background: var(--gradient-dark);
    color: white;
}

.gradient-purple-teal-section {
    background: var(--gradient-purple-teal);
    color: white;
}

/* Section spacing */
.section-spacing {
    margin-top: var(--section-spacing);
    margin-bottom: var(--section-spacing);
}

.section-spacing-top {
    margin-top: var(--section-spacing);
}

.section-spacing-bottom {
    margin-bottom: var(--section-spacing);
}

/* ===== GLASSMORPHIC CARDS ===== */

/* Basic glassmorphic card */
.glass-card {
    background-color: var(--glass-bg-medium);
    backdrop-filter: var(--glass-blur-medium);
    -webkit-backdrop-filter: var(--glass-blur-medium);
    border-radius: 16px;
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: 30px;
    transition: all 0.3s ease;
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* Strong glassmorphic effect */
.glass-card-strong {
    background-color: var(--glass-bg-strong);
    backdrop-filter: var(--glass-blur-strong);
    -webkit-backdrop-filter: var(--glass-blur-strong);
    border-radius: 16px;
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: 30px;
    transition: all 0.3s ease;
}

/* Light glassmorphic effect */
.glass-card-light {
    background-color: var(--glass-bg-light);
    backdrop-filter: var(--glass-blur-light);
    -webkit-backdrop-filter: var(--glass-blur-light);
    border-radius: 16px;
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: 30px;
    transition: all 0.3s ease;
}

/* Frosted glass effect */
.frosted-glass {
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px) saturate(180%);
    -webkit-backdrop-filter: blur(10px) saturate(180%);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.125);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
}

/* ===== ENHANCED CARDS FOR SPECIFIC SECTIONS ===== */

/* Service cards with glassmorphism */
.service-card {
    background-color: var(--glass-bg-medium);
    backdrop-filter: var(--glass-blur-medium);
    -webkit-backdrop-filter: var(--glass-blur-medium);
    border-radius: 16px;
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: 30px;
    transition: all 0.3s ease;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-purple-teal);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* Industry cards with glassmorphism */
.industry-card {
    background-color: var(--glass-bg-medium);
    backdrop-filter: var(--glass-blur-medium);
    -webkit-backdrop-filter: var(--glass-blur-medium);
    border-radius: 16px;
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: 30px;
    transition: all 0.3s ease;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.industry-card::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: var(--gradient-primary);
    opacity: 0.1;
    border-radius: 50%;
    transform: translate(50%, 50%);
    transition: all 0.3s ease;
}

.industry-card:hover::after {
    transform: translate(30%, 30%) scale(1.2);
}

.industry-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* Contact cards with glassmorphism */
.contact-card {
    background-color: var(--glass-bg-medium);
    backdrop-filter: var(--glass-blur-medium);
    -webkit-backdrop-filter: var(--glass-blur-medium);
    border-radius: 16px;
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: 30px;
    transition: all 0.3s ease;
    margin-bottom: 30px;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.contact-icon {
    background: var(--gradient-primary);
}

/* ===== ENHANCED DETAIL SECTIONS ===== */

/* Service detail sections */
.service-detail {
    padding: 80px 0;
    position: relative;
}

.service-detail:nth-child(odd) {
    background-color: var(--bg-color);
}

.service-detail:nth-child(even) {
    background-color: var(--bg-secondary);
    position: relative;
    overflow: hidden;
}

.service-detail:nth-child(even)::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/textures/subtle-dots.png');
    opacity: 0.05;
    pointer-events: none;
}

/* Industry detail sections */
.industry-detail {
    padding: 80px 0;
    margin-bottom: 40px;
    position: relative;
}

.industry-detail:nth-child(odd) {
    background-color: var(--bg-color);
}

.industry-detail:nth-child(even) {
    background-color: var(--bg-secondary);
    position: relative;
    overflow: hidden;
}

.industry-detail:nth-child(even)::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/textures/subtle-dots.png');
    opacity: 0.05;
    pointer-events: none;
}

/* ===== ENHANCED FORM ELEMENTS ===== */

/* Glassmorphic form container */
.form-container {
    background-color: var(--glass-bg-medium);
    backdrop-filter: var(--glass-blur-medium);
    -webkit-backdrop-filter: var(--glass-blur-medium);
    border-radius: 16px;
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: 40px;
    margin-bottom: 40px;
}

/* Form inputs with enhanced styling */
.form-group input,
.form-group textarea,
.form-group select {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 12px 15px;
    width: 100%;
    color: var(--text-color);
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(92, 43, 226, 0.1);
    background-color: rgba(255, 255, 255, 0.15);
}

.dark-theme .form-group input,
.dark-theme .form-group textarea,
.dark-theme .form-group select {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .form-group input:focus,
.dark-theme .form-group textarea:focus,
.dark-theme .form-group select:focus {
    background-color: rgba(0, 0, 0, 0.3);
}

/* ===== ENHANCED BUTTONS ===== */

/* Gradient buttons */
.btn.gradient-btn {
    background: var(--gradient-primary);
    border: none;
    color: white;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.btn.gradient-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-secondary);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn.gradient-btn:hover::before {
    opacity: 1;
}

/* Glassmorphic buttons */
.btn.glass-btn {
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-color);
}

.btn.glass-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* ===== ENHANCED HERO SECTION ===== */

.hero {
    position: relative;
    overflow: hidden;
    padding: 100px 0;
    background-color: var(--bg-color);
}

.hero.gradient-hero {
    background: var(--gradient-primary);
    color: white;
}

.hero.pattern-hero {
    position: relative;
}

.hero.pattern-hero::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/textures/subtle-dots.png');
    opacity: 0.05;
    pointer-events: none;
}

/* ===== ENHANCED TRACKING PAGE ===== */

/* Tracking box with enhanced glassmorphism */
.tracking-box {
    background-color: var(--glass-bg-strong);
    backdrop-filter: var(--glass-blur-strong);
    -webkit-backdrop-filter: var(--glass-blur-strong);
    border-radius: 16px;
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: 40px;
    margin-bottom: 40px;
}

/* Tracking detail cards */
.detail-card {
    background-color: var(--glass-bg-medium);
    backdrop-filter: var(--glass-blur-medium);
    -webkit-backdrop-filter: var(--glass-blur-medium);
    border-radius: 16px;
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.detail-card::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    opacity: 0.1;
    border-radius: 50%;
    transform: translate(50%, 50%);
}

.detail-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* Tracking timeline with enhanced styling */
.tracking-timeline {
    position: relative;
    padding-left: 30px;
}

.tracking-timeline::before {
    content: "";
    position: absolute;
    top: 0;
    left: 15px;
    width: 2px;
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 1px;
}

.timeline-item {
    position: relative;
    padding-bottom: 30px;
}

.timeline-item:last-child {
    padding-bottom: 0;
}

.timeline-point {
    position: absolute;
    left: -30px;
    width: 30px;
    height: 30px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    box-shadow: var(--shadow-md);
}

.timeline-content {
    background-color: var(--glass-bg-light);
    backdrop-filter: var(--glass-blur-light);
    -webkit-backdrop-filter: var(--glass-blur-light);
    border-radius: 16px;
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: 20px;
    margin-bottom: 0;
    transition: all 0.3s ease;
}

.timeline-content:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow-lg);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 992px) {
    .service-detail, .industry-detail {
        padding: 60px 0;
    }

    .form-container, .tracking-box {
        padding: 30px;
    }
}

@media (max-width: 768px) {
    .service-detail, .industry-detail {
        padding: 40px 0;
    }

    .form-container, .tracking-box {
        padding: 20px;
    }

    .glass-card, .glass-card-strong, .glass-card-light {
        padding: 20px;
    }
}

/* ===== DARK THEME ADJUSTMENTS ===== */

.dark-theme .hero.gradient-hero {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #2a1a6c 80%), url('https://images.unsplash.com/photo-1586528116493-d3f4b2d8e1b7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80') no-repeat center center/cover;
    background-blend-mode: multiply;
}

.dark-theme .textured-section::before {
    opacity: 0.03;
}

.dark-theme .service-detail:nth-child(even)::before,
.dark-theme .industry-detail:nth-child(even)::before {
    opacity: 0.03;
}

.dark-theme .timeline-content {
    background-color: var(--glass-bg-light);
}

.dark-theme .detail-card::after,
.dark-theme .industry-card::after {
    opacity: 0.05;
}

/* ===== ANIMATIONS ===== */

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.animate-slide-up {
    animation: slideUp 0.5s ease forwards;
}

.animate-slide-in {
    animation: slideIn 0.5s ease forwards;
}

/* Apply animations with delay for children */
.animate-children > * {
    opacity: 0;
}

.animate-children > *:nth-child(1) { animation: slideUp 0.5s ease forwards 0.1s; }
.animate-children > *:nth-child(2) { animation: slideUp 0.5s ease forwards 0.2s; }
.animate-children > *:nth-child(3) { animation: slideUp 0.5s ease forwards 0.3s; }
.animate-children > *:nth-child(4) { animation: slideUp 0.5s ease forwards 0.4s; }
.animate-children > *:nth-child(5) { animation: slideUp 0.5s ease forwards 0.5s; }
.animate-children > *:nth-child(6) { animation: slideUp 0.5s ease forwards 0.6s; }

/* Credentials Container */
.credentials-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 1.5rem 0;
}

.credential-box {
    background: var(--surface-color);
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.credential-box h5 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

/* Problem Cards */
.problems-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.problem-card {
    background: var(--surface-color);
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.problem-card h4 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.problem-item {
    margin-bottom: 1.5rem;
}

.solution {
    margin-top: 0.5rem;
    padding-left: 1rem;
    border-left: 3px solid var(--accent-color);
}

/* Security Checklist */
.security-checklist ul {
    list-style: none;
    padding: 0;
}

.security-checklist li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

/* Support Steps */
.support-steps {
    background: var(--surface-color);
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.support-steps ol {
    padding-left: 1.5rem;
}

.support-steps li {
    margin-bottom: 0.5rem;
}

