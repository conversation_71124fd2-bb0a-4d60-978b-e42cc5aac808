/* Logo Styles */
.site-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    text-decoration: none;
}

.logo-icon {
    position: relative;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-icon-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 8px;
    transform: rotate(45deg);
    box-shadow: 0 4px 10px rgba(92, 43, 226, 0.3);
    transition: all 0.3s ease;
}

.logo-icon-inner {
    position: relative;
    z-index: 2;
    color: white;
    font-size: 18px;
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-text-main {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--secondary-color);
    line-height: 1.1;
    letter-spacing: -0.5px;
    transition: color 0.3s ease;
}

.logo-text-tagline {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 400;
    letter-spacing: 0.5px;
    transition: color 0.3s ease;
}

/* Logo hover effects */
.site-logo:hover .logo-icon-bg {
    transform: rotate(135deg);
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
}

.site-logo:hover .logo-text-main {
    color: var(--primary-color);
}

/* Animation for the logo */
@keyframes pulse {
    0% {
        transform: rotate(45deg) scale(1);
    }
    50% {
        transform: rotate(45deg) scale(1.05);
    }
    100% {
        transform: rotate(45deg) scale(1);
    }
}

.site-logo:hover .logo-icon-bg {
    animation: pulse 1.5s infinite ease-in-out;
}

/* Dark theme adjustments */
.dark-theme .logo-text-main {
    color: white;
}

.dark-theme .logo-text-tagline {
    color: rgba(255, 255, 255, 0.7);
}

.dark-theme .site-logo:hover .logo-text-main {
    color: var(--secondary-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .logo-text-main {
        font-size: 1.2rem;
    }
    
    .logo-text-tagline {
        font-size: 0.7rem;
    }
    
    .logo-icon {
        width: 32px;
        height: 32px;
    }
}

@media (max-width: 480px) {
    .logo-text-tagline {
        display: none;
    }
}
