<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Get user information
$userId = $_SESSION['user_id'];
$userInfo = getUserById($userId);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $firstName = sanitize($_POST['first_name']);
    $lastName = sanitize($_POST['last_name']);
    $email = sanitize($_POST['email']);

    try {
        // Update user profile
        $stmt = $conn->prepare("UPDATE users SET first_name = ?, last_name = ?, email = ? WHERE id = ?");
        $result = $stmt->execute([$firstName, $lastName, $email, $userId]);

        if ($result) {
            // Set success message
            setSuccessNotification('Profile updated successfully');
        } else {
            setErrorNotification('Error updating profile');
        }

        // Redirect to prevent form resubmission
        header('Location: profile.php');
        exit;
    } catch (PDOException $e) {
        setErrorNotification('Error: ' . $e->getMessage());
    }
}

// Set page title
$pageTitle = 'Edit Profile';

// Include header
include_once 'includes/header.php';
?>

<div class="container">
    <div class="page-header">
        <h1>Edit Profile</h1>
        <p>Update your personal information</p>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="profile-sidebar glass-card">
                <div class="profile-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <h3><?php echo htmlspecialchars(($userInfo['first_name'] ?? $userInfo['username']) . ' ' . ($userInfo['last_name'] ?? '')); ?></h3>
                <p><?php echo htmlspecialchars($userInfo['email']); ?></p>
                <div class="profile-details">
                    <p><strong>Username:</strong> <?php echo htmlspecialchars($userInfo['username']); ?></p>
                    <p><strong>Account Type:</strong> <?php echo ucfirst(htmlspecialchars($userInfo['role'])); ?></p>
                    <p><strong>Member Since:</strong> <?php echo date('F j, Y', strtotime($userInfo['created_at'])); ?></p>
                </div>
                <div class="profile-actions">
                    <a href="change-password.php" class="btn outline-btn">Change Password</a>
                </div>
            </div>

            <div class="quick-links glass-card">
                <h3>Quick Links</h3>
                <ul>
                    <li>
                        <a href="index.php">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="shipments.php">
                            <i class="fas fa-box"></i>
                            <span>My Shipments</span>
                        </a>
                    </li>
                    <li>
                        <a href="tracking.php">
                            <i class="fas fa-search"></i>
                            <span>Track Shipment</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="col-md-8">
            <div class="profile-form glass-card">
                <h2>Personal Information</h2>
                <form method="POST" action="profile.php">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" value="<?php echo htmlspecialchars($userInfo['username']); ?>" disabled>
                        <small>Username cannot be changed</small>
                    </div>

                    <div class="form-group">
                        <label for="first_name">First Name</label>
                        <input type="text" id="first_name" name="first_name" value="<?php echo htmlspecialchars($userInfo['first_name'] ?? ''); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="last_name">Last Name</label>
                        <input type="text" id="last_name" name="last_name" value="<?php echo htmlspecialchars($userInfo['last_name'] ?? ''); ?>">
                    </div>

                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($userInfo['email']); ?>" required>
                    </div>

                    <div class="form-actions">
                        <button type="submit" name="update_profile" class="btn primary-btn">Save Changes</button>
                        <a href="index.php" class="btn outline-btn">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
    .page-header {
        margin-bottom: 30px;
        padding-top: 30px;
    }

    .page-header h1 {
        color: var(--primary-color);
        margin-bottom: 10px;
    }

    .glass-card {
        background-color: var(--glass-bg);
        border-radius: 16px;
        padding: 25px;
        margin-bottom: 30px;
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: var(--glass-border);
        box-shadow: var(--glass-shadow);
    }

    .profile-sidebar {
        text-align: center;
    }

    .profile-avatar {
        width: 100px;
        height: 100px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 2.5rem;
    }

    .profile-sidebar h3 {
        margin-bottom: 5px;
        color: var(--primary-color);
    }

    .profile-sidebar > p {
        color: var(--text-secondary);
        margin-bottom: 20px;
    }

    .profile-details {
        text-align: left;
        margin: 20px 0;
        padding-top: 20px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
    }

    .profile-details p {
        margin-bottom: 10px;
    }

    .profile-actions {
        margin-top: 20px;
    }

    .quick-links {
        margin-top: 30px;
    }

    .quick-links h3 {
        margin-bottom: 15px;
        color: var(--primary-color);
    }

    .quick-links ul {
        list-style: none;
        padding: 0;
    }

    .quick-links li {
        margin-bottom: 12px;
    }

    .quick-links a {
        display: flex;
        align-items: center;
        gap: 10px;
        color: var(--text-primary);
        text-decoration: none;
        padding: 8px 0;
        transition: all 0.3s ease;
    }

    .quick-links a:hover {
        color: var(--primary-color);
        transform: translateX(5px);
    }

    .quick-links i {
        color: var(--primary-color);
        width: 20px;
        text-align: center;
    }

    .profile-form h2 {
        color: var(--primary-color);
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .form-group input {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        background-color: rgba(255, 255, 255, 0.8);
    }

    .form-group input:disabled {
        background-color: rgba(0, 0, 0, 0.05);
        cursor: not-allowed;
    }

    .form-group small {
        display: block;
        margin-top: 5px;
        color: var(--text-secondary);
    }

    .form-actions {
        display: flex;
        gap: 15px;
        margin-top: 30px;
    }

    /* Dark theme adjustments */
    .dark-theme .glass-card {
        background-color: rgba(30, 30, 40, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dark-theme .profile-details {
        border-top-color: rgba(255, 255, 255, 0.1);
    }

    .dark-theme .form-group input {
        background-color: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.1);
        color: white;
    }

    .dark-theme .form-group input:disabled {
        background-color: rgba(0, 0, 0, 0.3);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .row {
            flex-direction: column;
        }

        .col-md-4, .col-md-8 {
            width: 100%;
        }
    }
</style>

<?php
// Include footer
include_once 'includes/footer.php';
?>
