/**
 * Apply system settings to the site
 */
document.addEventListener('DOMContentLoaded', function() {
    // Apply custom colors from settings
    const primaryColor = document.documentElement.getAttribute('data-primary-color');
    const secondaryColor = document.documentElement.getAttribute('data-secondary-color');

    if (primaryColor) {
        document.documentElement.style.setProperty('--primary-color', primaryColor);
    }

    if (secondaryColor) {
        document.documentElement.style.setProperty('--secondary-color', secondaryColor);
    }

    // Apply dark mode setting
    const enableDarkMode = document.documentElement.getAttribute('data-enable-dark-mode');
    const darkModeToggle = document.getElementById('theme-toggle');

    if (enableDarkMode === '0' && darkModeToggle) {
        darkModeToggle.parentElement.style.display = 'none';
    }

    // Apply maintenance mode
    const maintenanceMode = document.documentElement.getAttribute('data-maintenance-mode');
    const maintenanceMessage = document.documentElement.getAttribute('data-maintenance-message');

    // Only show maintenance mode for non-admin pages and login page
    // This allows admins to still access the admin panel even when maintenance mode is on
    const isAdminPage = document.body.classList.contains('admin-page');
    const isLoginPage = window.location.pathname.includes('login.php');

    if (maintenanceMode === '1' && !isAdminPage && !isLoginPage) {
        // Create maintenance overlay
        const overlay = document.createElement('div');
        overlay.className = 'maintenance-overlay';
        overlay.id = 'maintenance-overlay';

        const messageBox = document.createElement('div');
        messageBox.className = 'maintenance-message';

        const heading = document.createElement('h2');
        heading.textContent = 'Site Maintenance';

        const message = document.createElement('p');
        message.textContent = maintenanceMessage || 'We are currently performing scheduled maintenance. Please check back soon.';

        // Add admin login link if not on login page
        if (!isLoginPage) {
            const loginLink = document.createElement('a');
            loginLink.href = 'login.php';
            loginLink.className = 'admin-login-link';
            loginLink.textContent = 'Admin Login';
            messageBox.appendChild(document.createElement('br'));
            messageBox.appendChild(loginLink);
        }

        messageBox.appendChild(heading);
        messageBox.appendChild(message);

        // Add to body
        document.body.appendChild(overlay);
        document.body.classList.add('maintenance-mode');

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .maintenance-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.8);
                z-index: 9999;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .maintenance-message {
                background-color: var(--bg-secondary);
                border-radius: 10px;
                padding: 30px;
                max-width: 500px;
                text-align: center;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            }

            .maintenance-message h2 {
                color: var(--primary-color);
                margin-top: 0;
            }

            body.maintenance-mode {
                overflow: hidden;
            }

            .admin-login-link {
                display: inline-block;
                margin-top: 20px;
                color: var(--primary-color);
                text-decoration: none;
                padding: 8px 15px;
                border: 1px solid var(--primary-color);
                border-radius: 5px;
                transition: all 0.3s ease;
            }

            .admin-login-link:hover {
                background-color: var(--primary-color);
                color: white;
            }
        `;
        document.head.appendChild(style);
    }
});
